//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Technical {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Technical() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Technical", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosya Ekle.
        /// </summary>
        internal static string AddFile {
            get {
                return ResourceManager.GetString("AddFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detay.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosya Yolu.
        /// </summary>
        internal static string FilePath {
            get {
                return ResourceManager.GetString("FilePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün.
        /// </summary>
        internal static string Item {
            get {
                return ResourceManager.GetString("Item", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proje Adı.
        /// </summary>
        internal static string ProjectName {
            get {
                return ResourceManager.GetString("ProjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep.
        /// </summary>
        internal static string Request {
            get {
                return ResourceManager.GetString("Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Cevaplandı.
        /// </summary>
        internal static string RequestAnswered {
            get {
                return ResourceManager.GetString("RequestAnswered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Eden.
        /// </summary>
        internal static string RequesterName {
            get {
                return ResourceManager.GetString("RequesterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Kapandı.
        /// </summary>
        internal static string RequestFinished {
            get {
                return ResourceManager.GetString("RequestFinished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Adı.
        /// </summary>
        internal static string RequestName {
            get {
                return ResourceManager.GetString("RequestName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Alındı.
        /// </summary>
        internal static string RequestSended {
            get {
                return ResourceManager.GetString("RequestSended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Güncellendi.
        /// </summary>
        internal static string RequestUpdated {
            get {
                return ResourceManager.GetString("RequestUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçiniz.
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gönder.
        /// </summary>
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durum.
        /// </summary>
        internal static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saat.
        /// </summary>
        internal static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cevap Yaz.
        /// </summary>
        internal static string WriteReply {
            get {
                return ResourceManager.GetString("WriteReply", resourceCulture);
            }
        }
    }
}
