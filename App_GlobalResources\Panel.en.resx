﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
    <comment>General</comment>
  </data>
  <data name="AllList" xml:space="preserve">
    <value>All List</value>
    <comment>General</comment>
  </data>
  <data name="Answered" xml:space="preserve">
    <value>Answered</value>
    <comment>General</comment>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>Confirmation Pending</value>
    <comment>General</comment>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Customs" xml:space="preserve">
    <value>Customs</value>
    <comment>Purchase</comment>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Day</value>
    <comment>Purchase</comment>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="DeliveryTimeCalculation" xml:space="preserve">
    <value>Delivery Time Calculation</value>
  </data>
  <data name="Description1" xml:space="preserve">
    <value>Description 1</value>
  </data>
  <data name="Description2" xml:space="preserve">
    <value>Description 2</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="DocumentNo" xml:space="preserve">
    <value>Document No</value>
    <comment>General</comment>
  </data>
  <data name="EnteredPromisedDate" xml:space="preserve">
    <value>Entered Promised Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="ExpectedDate" xml:space="preserve">
    <value>Expected Date</value>
    <comment>General</comment>
  </data>
  <data name="LastModifiedDate" xml:space="preserve">
    <value>Last Modified Date</value>
  </data>
  <data name="LastModifiedUser" xml:space="preserve">
    <value>Last Modified User</value>
  </data>
  <data name="Log" xml:space="preserve">
    <value>Log</value>
    <comment>General</comment>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
    <comment>General</comment>
  </data>
  <data name="Logistic" xml:space="preserve">
    <value>Logistic</value>
    <comment>Purchase</comment>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
    <comment>General</comment>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
    <comment>General</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
    <comment>General</comment>
  </data>
  <data name="NumberedOrder" xml:space="preserve">
    <value>Numbered Order</value>
    <comment>Purchase</comment>
  </data>
  <data name="OldItemNo" xml:space="preserve">
    <value>Old Item No</value>
    <comment>General</comment>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Order</value>
    <comment>General</comment>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>Order Date</value>
    <comment>General</comment>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Orders</value>
    <comment>Purchase</comment>
  </data>
  <data name="Pass" xml:space="preserve">
    <value>Password</value>
    <comment>General</comment>
  </data>
  <data name="PendingPromisedDate" xml:space="preserve">
    <value>Pending Promised Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="PortalStatus" xml:space="preserve">
    <value>Portal Status</value>
  </data>
  <data name="PreviousPromisedDate" xml:space="preserve">
    <value>Previous Promised Date</value>
  </data>
  <data name="PromisedDate" xml:space="preserve">
    <value>Promised Date</value>
    <comment>General</comment>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Purchase</value>
    <comment>General</comment>
  </data>
  <data name="PurchaseDescription" xml:space="preserve">
    <value>Purchase Description</value>
  </data>
  <data name="PurchaseList" xml:space="preserve">
    <value>Purchase List</value>
    <comment>General</comment>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="ReceiveDate" xml:space="preserve">
    <value>Receive Date</value>
  </data>
  <data name="RemainderQuantity" xml:space="preserve">
    <value>Remainder Quantity</value>
  </data>
  <data name="RequestedDate" xml:space="preserve">
    <value>Requested Date</value>
    <comment>General</comment>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Sales</value>
    <comment>General</comment>
  </data>
  <data name="TechnicalQuestions" xml:space="preserve">
    <value>Technical Questions</value>
    <comment>General</comment>
  </data>
  <data name="UserID" xml:space="preserve">
    <value>User ID</value>
    <comment>General</comment>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
    <comment>General</comment>
  </data>
  <data name="VendorDesc1" xml:space="preserve">
    <value>Vendor Description</value>
  </data>
  <data name="VendorDesc2" xml:space="preserve">
    <value>Vendor Description 2</value>
  </data>
  <data name="WarehouseProcessingTime" xml:space="preserve">
    <value>Warehouse Processing Time</value>
  </data>
  <data name="Weekly" xml:space="preserve">
    <value>Weekly</value>
    <comment>Purchase</comment>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="hDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>Add New User</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="RoleDescription" xml:space="preserve">
    <value>RoleDescription</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="UsersAndRoles" xml:space="preserve">
    <value>Users and Roles</value>
  </data>
  <data name="VendorFilter" xml:space="preserve">
    <value>Vendor Filter</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>Add User</value>
  </data>
  <data name="CustomsControl" xml:space="preserve">
    <value>Customs Control</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>User Detail</value>
  </data>
  <data name="AddedFiles" xml:space="preserve">
    <value>Added Files</value>
  </data>
  <data name="AddFiles" xml:space="preserve">
    <value>Add Files</value>
  </data>
  <data name="AllMyQuestions" xml:space="preserve">
    <value>My All Questions</value>
  </data>
  <data name="CreateNewQuestion" xml:space="preserve">
    <value>Create a New Question</value>
  </data>
  <data name="CreateBidUser" xml:space="preserve">
    <value>Bid's User</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Finished" xml:space="preserve">
    <value>Finished</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="MyQuestions" xml:space="preserve">
    <value>My Questions</value>
  </data>
  <data name="BidDate" xml:space="preserve">
    <value>Bid Date</value>
  </data>
  <data name="BidInformations" xml:space="preserve">
    <value>Bid Informations</value>
  </data>
  <data name="BidNo" xml:space="preserve">
    <value>Bid No</value>
  </data>
  <data name="Bids" xml:space="preserve">
    <value>Bids</value>
  </data>
  <data name="BidStatus" xml:space="preserve">
    <value>Bid Status</value>
  </data>
  <data name="PrintItemDetail" xml:space="preserve">
    <value>Print Item Details</value>
  </data>
  <data name="ProjectName" xml:space="preserve">
    <value>Project Name</value>
  </data>
  <data name="ProjectNo" xml:space="preserve">
    <value>Projec tNo</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Request</value>
  </data>
  <data name="RequestAnswered" xml:space="preserve">
    <value>Request Answered</value>
    <comment>2</comment>
  </data>
  <data name="RequesterName" xml:space="preserve">
    <value>Requester Name</value>
  </data>
  <data name="RequestFinished" xml:space="preserve">
    <value>Request Finished</value>
    <comment>3</comment>
  </data>
  <data name="RequestName" xml:space="preserve">
    <value>Request Name</value>
  </data>
  <data name="RequestSended" xml:space="preserve">
    <value>Request Sended</value>
    <comment>0</comment>
  </data>
  <data name="RequestUpdated" xml:space="preserve">
    <value>Request Updated</value>
    <comment>1</comment>
  </data>
  <data name="SalesControl" xml:space="preserve">
    <value>Sales Control</value>
  </data>
  <data name="SalesDetail" xml:space="preserve">
    <value>Sales Detail</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockCode" xml:space="preserve">
    <value>Stock Code</value>
  </data>
  <data name="TableView" xml:space="preserve">
    <value>Table View</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Tutation" xml:space="preserve">
    <value>Tutation</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="WriteReply" xml:space="preserve">
    <value>Reply</value>
  </data>
  <data name="iNotSaved" xml:space="preserve">
    <value>Fail! Please Try Again</value>
  </data>
  <data name="iSavedSuccessfully" xml:space="preserve">
    <value>Saved Successfully</value>
  </data>
  <data name="BidDetail" xml:space="preserve">
    <value>Bit Detail</value>
  </data>
  <data name="AllCustomsList" xml:space="preserve">
    <value>Empty or wrong item definitions for customs</value>
  </data>
  <data name="iAllCustomsList" xml:space="preserve">
    <value>Last 100 overseas purchase list for which item definition is empty or wrong</value>
  </data>
  <data name="iCustomsCard" xml:space="preserve">
    <value>Empty or Wrong Item Definition</value>
  </data>
  <data name="ItemDefinition" xml:space="preserve">
    <value>Item Definition</value>
  </data>
  <data name="SetNewDefinition" xml:space="preserve">
    <value>Set New Definition</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="VendorName" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="AddNewVendorUser" xml:space="preserve">
    <value>Add a New Vendor User</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Adress</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="iNewVendorUser" xml:space="preserve">
    <value>Vendors using Vendor no and password when login.</value>
  </data>
  <data name="iNewVendorUserError" xml:space="preserve">
    <value>This Vendor allready created or incorrect vendor no</value>
  </data>
  <data name="ListVendorUser" xml:space="preserve">
    <value>Vendors</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="PortalName" xml:space="preserve">
    <value>Portal Name</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="VendorInformations" xml:space="preserve">
    <value>Vendor Informations</value>
  </data>
  <data name="VendorNo" xml:space="preserve">
    <value>Vendor No</value>
  </data>
  <data name="LoadingDate" xml:space="preserve">
    <value>Loading Date</value>
  </data>
  <data name="LoadingDLogisticType" xml:space="preserve">
    <value>Loading Date / Logistic Type</value>
  </data>
  <data name="LogisticType" xml:space="preserve">
    <value>Logistic Type</value>
  </data>
  <data name="Minivan" xml:space="preserve">
    <value>Minivan</value>
  </data>
  <data name="Plane" xml:space="preserve">
    <value>Plane</value>
  </data>
  <data name="Truck" xml:space="preserve">
    <value>Truck</value>
  </data>
  <data name="ItemReady" xml:space="preserve">
    <value>Ready</value>
  </data>
  <data name="ItemReadyDate" xml:space="preserve">
    <value>Item Ready Date</value>
  </data>
  <data name="Until" xml:space="preserve">
    <value>Until</value>
  </data>
  <data name="WaitingLogistic" xml:space="preserve">
    <value>Waiting</value>
  </data>
  <data name="CustomsRequest" xml:space="preserve">
    <value>Customs Request</value>
  </data>
  <data name="CustomsRequestDetail" xml:space="preserve">
    <value>Customs Request Detail</value>
  </data>
  <data name="GoToItemDef" xml:space="preserve">
    <value>Go to Item Definitiom</value>
  </data>
  <data name="InformationAdded" xml:space="preserve">
    <value>Information Added</value>
  </data>
  <data name="InformationRequested" xml:space="preserve">
    <value>Information Requested</value>
  </data>
  <data name="RequestInformation" xml:space="preserve">
    <value>Request Information</value>
  </data>
  <data name="WaitingInformation" xml:space="preserve">
    <value>Waiting Information</value>
  </data>
  <data name="AskNewInfo" xml:space="preserve">
    <value>Ask New Information</value>
  </data>
  <data name="ChangePicture" xml:space="preserve">
    <value>Change Picture</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Codes" xml:space="preserve">
    <value>Codes</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="CreateGroup" xml:space="preserve">
    <value>Create Group</value>
    <comment>Item_List</comment>
  </data>
  <data name="CreateItem" xml:space="preserve">
    <value>Create Item</value>
    <comment>Item_List</comment>
  </data>
  <data name="CreateSpecSheetGroup" xml:space="preserve">
    <value>Create group for Specification Sheet</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="DeleteGroup" xml:space="preserve">
    <value>Delete Group</value>
    <comment>Item_List</comment>
  </data>
  <data name="DeleteItem" xml:space="preserve">
    <value>Delete Item</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="DeleteMsg1" xml:space="preserve">
    <value>This group have</value>
    <comment>Item_List</comment>
  </data>
  <data name="DeleteMsg2" xml:space="preserve">
    <value>sub item. All sub Items will be remove.</value>
    <comment>Item_List</comment>
  </data>
  <data name="DeleteSpecs" xml:space="preserve">
    <value>Delete Specificaiton</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="EditSpecs" xml:space="preserve">
    <value>Edit Specification</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
    <comment>Item_List</comment>
  </data>
  <data name="ItemDetail" xml:space="preserve">
    <value>Item Detail</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="ItemList" xml:space="preserve">
    <value>Item List</value>
    <comment>Item_List</comment>
  </data>
  <data name="ItemSpecsTab" xml:space="preserve">
    <value>Item Specification</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="NewSpecBtn" xml:space="preserve">
    <value>New Specification</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="OldItemNoPattern" xml:space="preserve">
    <value>Old Item No Pattern</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="OldItemNoWizard" xml:space="preserve">
    <value>Old Item No Wizard</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Path" xml:space="preserve">
    <value>Path</value>
    <comment>Item_List</comment>
  </data>
  <data name="Picture" xml:space="preserve">
    <value>Picture</value>
    <comment>Item_List</comment>
  </data>
  <data name="PreviewBtn" xml:space="preserve">
    <value>Preview</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SelectPicture" xml:space="preserve">
    <value>Select Picture</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SpecOrder" xml:space="preserve">
    <value>Specification Order</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SpecSheetOrder" xml:space="preserve">
    <value>Specification Sheet Order</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SpecSheetTab" xml:space="preserve">
    <value>Specification Sheet</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Values" xml:space="preserve">
    <value>Values</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="CheckValueCode" xml:space="preserve">
    <value>Check value and code size</value>
    <comment>Item Detail</comment>
  </data>
  <data name="AllNotification" xml:space="preserve">
    <value>All Notifications</value>
  </data>
  <data name="ShowAll" xml:space="preserve">
    <value>Show All</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
</root>