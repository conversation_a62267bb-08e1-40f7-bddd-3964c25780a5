﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
    <comment>General</comment>
  </data>
  <data name="AllList" xml:space="preserve">
    <value>Tüm Liste</value>
    <comment>General</comment>
  </data>
  <data name="Answered" xml:space="preserve">
    <value>Cevaplanan</value>
    <comment>General</comment>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>Onay Bekleyen</value>
    <comment>General</comment>
  </data>
  <data name="DocumentNo" xml:space="preserve">
    <value>Belge No</value>
    <comment>General</comment>
  </data>
  <data name="ExpectedDate" xml:space="preserve">
    <value>Beklenen Tarih</value>
    <comment>General</comment>
  </data>
  <data name="Log" xml:space="preserve">
    <value>İşlem Dökümü</value>
    <comment>General</comment>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Giriş Yap</value>
    <comment>General</comment>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Çıkış Yap</value>
    <comment>General</comment>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Adı</value>
    <comment>General</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
    <comment>General</comment>
  </data>
  <data name="OldItemNo" xml:space="preserve">
    <value>Eski Stok No</value>
    <comment>General</comment>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Sipariş</value>
    <comment>General</comment>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>Sipariş Tarihi</value>
    <comment>General</comment>
  </data>
  <data name="Pass" xml:space="preserve">
    <value>Şifre</value>
    <comment>General</comment>
  </data>
  <data name="PromisedDate" xml:space="preserve">
    <value>Taahhüt Edilen Teslim Tarihi</value>
    <comment>General</comment>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Satınalma</value>
    <comment>General</comment>
  </data>
  <data name="PurchaseList" xml:space="preserve">
    <value>Satınalma Listesi</value>
    <comment>General</comment>
  </data>
  <data name="RequestedDate" xml:space="preserve">
    <value>İstenilen Tarih</value>
    <comment>General</comment>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Satış</value>
    <comment>General</comment>
  </data>
  <data name="TechnicalQuestions" xml:space="preserve">
    <value>Teknik Sorular</value>
    <comment>General</comment>
  </data>
  <data name="UserID" xml:space="preserve">
    <value>Kullanıcı ID</value>
    <comment>General</comment>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Kullanıcılar</value>
    <comment>General</comment>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Geri Dön</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Oluştur</value>
  </data>
  <data name="Customs" xml:space="preserve">
    <value>Gümrük</value>
    <comment>Purchase</comment>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Gün</value>
    <comment>Purchase</comment>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Gönderim Tarihi</value>
  </data>
  <data name="DeliveryTimeCalculation" xml:space="preserve">
    <value>Sağlama Süresi Hesaplaması</value>
  </data>
  <data name="Description1" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="Description2" xml:space="preserve">
    <value>Açıklama 2</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detay</value>
  </data>
  <data name="EnteredPromisedDate" xml:space="preserve">
    <value>Termin Verilen Siparişler</value>
    <comment>Purchase</comment>
  </data>
  <data name="LastModifiedDate" xml:space="preserve">
    <value>Portal Son Değişiklik Tarihi</value>
  </data>
  <data name="LastModifiedUser" xml:space="preserve">
    <value>Portal Son Degisiklik Kullanıcı</value>
  </data>
  <data name="Logistic" xml:space="preserve">
    <value>Lojistik</value>
    <comment>Purchase</comment>
  </data>
  <data name="NumberedOrder" xml:space="preserve">
    <value>Numaralı Sipariş</value>
    <comment>Purchase</comment>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Siparişler</value>
    <comment>Purchase</comment>
  </data>
  <data name="PendingPromisedDate" xml:space="preserve">
    <value>Termin Bekleyen Siparişler</value>
    <comment>Purchase</comment>
  </data>
  <data name="PortalStatus" xml:space="preserve">
    <value>Portal Durumu</value>
  </data>
  <data name="PreviousPromisedDate" xml:space="preserve">
    <value>Bir Önceki Termin Tarihi</value>
  </data>
  <data name="PurchaseDescription" xml:space="preserve">
    <value>Satınalma Açıklaması</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Miktar</value>
  </data>
  <data name="ReceiveDate" xml:space="preserve">
    <value>Alış Tarihi</value>
  </data>
  <data name="RemainderQuantity" xml:space="preserve">
    <value>Kalan Miktar</value>
  </data>
  <data name="VendorDesc1" xml:space="preserve">
    <value>Tedarikçi Özel Durum Açıklama</value>
  </data>
  <data name="VendorDesc2" xml:space="preserve">
    <value>Tedarikçi Özel Durum Açıklama 2</value>
  </data>
  <data name="WarehouseProcessingTime" xml:space="preserve">
    <value>Giriş Ambarı İşlem Süresi</value>
  </data>
  <data name="Weekly" xml:space="preserve">
    <value>Haftalık</value>
    <comment>Purchase</comment>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Sil</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Düzenle</value>
  </data>
  <data name="hDelete" xml:space="preserve">
    <value>Silme İşlemi</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Ay</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>Yeni Kullanıcı Ekle</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Rol</value>
  </data>
  <data name="RoleDescription" xml:space="preserve">
    <value>Role Description</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Türkçe</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Kullanıcı</value>
  </data>
  <data name="UsersAndRoles" xml:space="preserve">
    <value>Kullanıcılar ve Roller</value>
  </data>
  <data name="VendorFilter" xml:space="preserve">
    <value>Firma Filtrele</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Hoş Geldiniz</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Yıl</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>Kullanıcı Ekle</value>
  </data>
  <data name="CustomsControl" xml:space="preserve">
    <value>Gümrük Kontrol</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Kaydet</value>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>Kullanıcı Detayı</value>
  </data>
  <data name="AddedFiles" xml:space="preserve">
    <value>Eklenen Dosyalar</value>
  </data>
  <data name="AddFiles" xml:space="preserve">
    <value>Dosya Ekle</value>
  </data>
  <data name="AllMyQuestions" xml:space="preserve">
    <value>Tüm Sorularım</value>
  </data>
  <data name="CreateNewQuestion" xml:space="preserve">
    <value>Yeni Soru Oluştur</value>
  </data>
  <data name="CreateBidUser" xml:space="preserve">
    <value>Teklifi oluşturan kullanıcı</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Müşteri Adı</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Tarih</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="Finished" xml:space="preserve">
    <value>Tamamlandı</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Ürün</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Ürünler</value>
  </data>
  <data name="MyQuestions" xml:space="preserve">
    <value>Sorularım</value>
  </data>
  <data name="BidDate" xml:space="preserve">
    <value>Teklif Tarihi</value>
  </data>
  <data name="BidInformations" xml:space="preserve">
    <value>Teklif Bilgileri</value>
  </data>
  <data name="BidNo" xml:space="preserve">
    <value>Teklif No</value>
  </data>
  <data name="Bids" xml:space="preserve">
    <value>Teklifler</value>
  </data>
  <data name="BidStatus" xml:space="preserve">
    <value>Teklif Durumu</value>
  </data>
  <data name="PrintItemDetail" xml:space="preserve">
    <value>Ürün Detay Çıktısı</value>
  </data>
  <data name="ProjectName" xml:space="preserve">
    <value>Proje Adı</value>
  </data>
  <data name="ProjectNo" xml:space="preserve">
    <value>Proje No</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Talep</value>
  </data>
  <data name="RequestAnswered" xml:space="preserve">
    <value>Talebiniz Cevaplandı</value>
    <comment>2</comment>
  </data>
  <data name="RequesterName" xml:space="preserve">
    <value>Talep Eden</value>
  </data>
  <data name="RequestFinished" xml:space="preserve">
    <value>Talebiniz Kapandı</value>
    <comment>3</comment>
  </data>
  <data name="RequestName" xml:space="preserve">
    <value>Talep Adı</value>
  </data>
  <data name="RequestSended" xml:space="preserve">
    <value>Talep Gönderildi</value>
    <comment>0</comment>
  </data>
  <data name="RequestUpdated" xml:space="preserve">
    <value>Talebiniz Güncellendi</value>
    <comment>1</comment>
  </data>
  <data name="SalesControl" xml:space="preserve">
    <value>Satış Kontrolü</value>
  </data>
  <data name="SalesDetail" xml:space="preserve">
    <value>Satış Detayı</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Seçiniz</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Gönder</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Durum</value>
  </data>
  <data name="StockCode" xml:space="preserve">
    <value>Stok Kodu</value>
  </data>
  <data name="TableView" xml:space="preserve">
    <value>Tablo Görünümü</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Saat</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Toplam</value>
  </data>
  <data name="Tutation" xml:space="preserve">
    <value>Tutar</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Birim Fiyat</value>
  </data>
  <data name="WriteReply" xml:space="preserve">
    <value>Cevapla</value>
  </data>
  <data name="iNotSaved" xml:space="preserve">
    <value>Kaydedilemedi! Lütfen Sonra Tekrar Deneyin</value>
  </data>
  <data name="iSavedSuccessfully" xml:space="preserve">
    <value>Kaydedildi</value>
  </data>
  <data name="BidDetail" xml:space="preserve">
    <value>Teklif Detayı</value>
  </data>
  <data name="AllCustomsList" xml:space="preserve">
    <value>Gümrük için eksik tanımlı ürünler</value>
  </data>
  <data name="iAllCustomsList" xml:space="preserve">
    <value>Ürün tanımının boş veya yanlış olan son 100 yurtdışı satın alma listesi</value>
  </data>
  <data name="iCustomsCard" xml:space="preserve">
    <value>Eksik veya Hatalı Mal Tanımı</value>
  </data>
  <data name="ItemDefinition" xml:space="preserve">
    <value>Mal Tanımı</value>
  </data>
  <data name="SetNewDefinition" xml:space="preserve">
    <value>Yeni Tanım Ayarla</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Ara</value>
  </data>
  <data name="VendorName" xml:space="preserve">
    <value>Firma Adı</value>
  </data>
  <data name="AddNewVendorUser" xml:space="preserve">
    <value>Yeni Tedarikçi Hesabı Oluştur</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Adres</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Yetkili</value>
  </data>
  <data name="iNewVendorUser" xml:space="preserve">
    <value>Tedarikçi no ve şifresi kullanılarak giriş yapılacaktır.</value>
  </data>
  <data name="iNewVendorUserError" xml:space="preserve">
    <value>Tedarikçi no yanlış yada daha önceden eklendi</value>
  </data>
  <data name="ListVendorUser" xml:space="preserve">
    <value>Tedarikçiler</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Şifre</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="PortalName" xml:space="preserve">
    <value>Portal Adı</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Şifreyi Sıfırla</value>
  </data>
  <data name="VendorInformations" xml:space="preserve">
    <value>Tedarikçi Bilgileri</value>
  </data>
  <data name="VendorNo" xml:space="preserve">
    <value>Tedarikçi No</value>
  </data>
  <data name="LoadingDate" xml:space="preserve">
    <value>Toplama Tarihi</value>
  </data>
  <data name="LoadingDLogisticType" xml:space="preserve">
    <value>Toplama Tarihi (E kadar) / Taşıma Yöntemi</value>
  </data>
  <data name="LogisticType" xml:space="preserve">
    <value>Lojistik Tipi</value>
  </data>
  <data name="Minivan" xml:space="preserve">
    <value>Minivan</value>
  </data>
  <data name="Plane" xml:space="preserve">
    <value>Uçak</value>
  </data>
  <data name="Truck" xml:space="preserve">
    <value>Tır</value>
  </data>
  <data name="ItemReady" xml:space="preserve">
    <value>Hazır</value>
  </data>
  <data name="ItemReadyDate" xml:space="preserve">
    <value>Ürünün Hazır Olduğu Tarih</value>
  </data>
  <data name="Until" xml:space="preserve">
    <value>E kadar</value>
  </data>
  <data name="WaitingLogistic" xml:space="preserve">
    <value>Bekleniyor</value>
  </data>
  <data name="CustomsRequest" xml:space="preserve">
    <value>Gümrük Bilgi İstekleri</value>
  </data>
  <data name="CustomsRequestDetail" xml:space="preserve">
    <value>Gümrük Bilgi İstek Detayı</value>
  </data>
  <data name="GoToItemDef" xml:space="preserve">
    <value>Mal tanımına git</value>
  </data>
  <data name="InformationAdded" xml:space="preserve">
    <value>Bilgi eklendi</value>
  </data>
  <data name="InformationRequested" xml:space="preserve">
    <value>Bilgi istendi</value>
  </data>
  <data name="RequestInformation" xml:space="preserve">
    <value>Bilgi İste</value>
  </data>
  <data name="WaitingInformation" xml:space="preserve">
    <value>Bilgi Bekleniyor</value>
  </data>
  <data name="AskNewInfo" xml:space="preserve">
    <value>Yeni Bilgi İste</value>
  </data>
  <data name="ChangePicture" xml:space="preserve">
    <value>Resmi Değiştir</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="CheckValueCode" xml:space="preserve">
    <value>Değer ve kod uzunluğunu kontrol edin</value>
    <comment>Item Detail</comment>
  </data>
  <data name="Codes" xml:space="preserve">
    <value>Kodları</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="CreateGroup" xml:space="preserve">
    <value>Grup Oluştur</value>
    <comment>Item_List</comment>
  </data>
  <data name="CreateItem" xml:space="preserve">
    <value>Ürün Oluştur</value>
    <comment>Item_List</comment>
  </data>
  <data name="CreateSpecSheetGroup" xml:space="preserve">
    <value>Özellik listesi grubu oluştur</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="DeleteGroup" xml:space="preserve">
    <value>Grubu Sil</value>
    <comment>Item_List</comment>
  </data>
  <data name="DeleteGrpMsg1" xml:space="preserve">
    <value>Bu Klasör</value>
    <comment>Item_List</comment>
  </data>
  <data name="DeleteGrpMsg2" xml:space="preserve">
    <value>alt öge içeriyor. Tüm alt öğeler silinecek.</value>
    <comment>Item_List</comment>
  </data>
  <data name="DeleteItem" xml:space="preserve">
    <value>Ürünü Sil</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="DeleteSpecs" xml:space="preserve">
    <value>Özelliği Sil</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="EditSpecs" xml:space="preserve">
    <value>Özelliği Düzenle</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Ev</value>
    <comment>Item_List</comment>
  </data>
  <data name="ItemDetail" xml:space="preserve">
    <value>Ürün Detayı</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="ItemList" xml:space="preserve">
    <value>Ürün Listesi</value>
    <comment>Item_List</comment>
  </data>
  <data name="ItemSpecsTab" xml:space="preserve">
    <value>Ürün Özellikleri</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="NewSpecBtn" xml:space="preserve">
    <value>Özellik Ekle</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="OldItemNoPattern" xml:space="preserve">
    <value>Old Item No Yapısı</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="OldItemNoWizard" xml:space="preserve">
    <value>Old Item No oluştur</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Path" xml:space="preserve">
    <value>Yol</value>
    <comment>Item_List</comment>
  </data>
  <data name="Picture" xml:space="preserve">
    <value>Resim</value>
    <comment>Item_List</comment>
  </data>
  <data name="PreviewBtn" xml:space="preserve">
    <value>Ön İzleme</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SelectPicture" xml:space="preserve">
    <value>Resim Seç</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SpecOrder" xml:space="preserve">
    <value>Özellik Sırası</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SpecSheetOrder" xml:space="preserve">
    <value>Özellik Listesi Sırası</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="SpecSheetTab" xml:space="preserve">
    <value>Özellik Listesi</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="Values" xml:space="preserve">
    <value>Değerleri</value>
    <comment>Item_Detail</comment>
  </data>
  <data name="AllNotification" xml:space="preserve">
    <value>Tüm Bildirimler</value>
  </data>
  <data name="ShowAll" xml:space="preserve">
    <value>Tümünü Göster</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Tamamlandı</value>
    <comment>Completed</comment>
  </data>
</root>