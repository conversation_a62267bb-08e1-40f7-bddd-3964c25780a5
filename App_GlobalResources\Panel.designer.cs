//------------------------------------------------------------------------------
// <auto-generated>
//     Bu kod araç tarafından oluşturuldu.
//     Çalışma Zamanı Sürümü:4.0.30319.42000
//
//     Bu dosyada yapılacak değişiklikler yanlış davranışa neden olabilir ve
//     kod yeniden oluşturulursa kaybolur.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   Yerelleştirilmiş dizeleri aramak gibi işlemler için, tü<PERSON><PERSON> kesin olarak belirtilmiş kaynak sınıfı.
    /// </summary>
    // Bu sınıf StronglyTypedResourceBuilder tarafından otomatik olarak oluşturuldu
    // sınıfı tarafından otomatik olarak oluşturuldu.
    // Üye eklemek veya kaldırmak için .ResX dosyanızı düzenleyin ve sonra da ResGen
    // /str seçeneği ile veya Visual Studio projesini yeniden kur.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Panel {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Panel() {
        }
        
        /// <summary>
        ///   Bu sınıf tarafından kullanılan, önbelleğe alınmış ResourceManager örneğini döndürür.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Panel", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Bu türü kesin olarak belirtilmiş kaynak sınıfını kullanarak geçerli iş parçacığının CurrentUICulture 
        ///   özelliğini tüm kaynak aramaları için geçersiz kılar.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Eklenen Dosyalar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AddedFiles {
            get {
                return ResourceManager.GetString("AddedFiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Dosya Ekle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AddFiles {
            get {
                return ResourceManager.GetString("AddFiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Tedarikçi Hesabı Oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AddNewVendorUser {
            get {
                return ResourceManager.GetString("AddNewVendorUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kullanıcı Ekle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AddUser {
            get {
                return ResourceManager.GetString("AddUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Admin benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Admin {
            get {
                return ResourceManager.GetString("Admin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Adres benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Adress {
            get {
                return ResourceManager.GetString("Adress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük için eksik tanımlı ürünler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AllCustomsList {
            get {
                return ResourceManager.GetString("AllCustomsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tüm Liste benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AllList {
            get {
                return ResourceManager.GetString("AllList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tüm Sorularım benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AllMyQuestions {
            get {
                return ResourceManager.GetString("AllMyQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tüm Bildirimler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AllNotification {
            get {
                return ResourceManager.GetString("AllNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Cevaplanan benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Answered {
            get {
                return ResourceManager.GetString("Answered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Bilgi İste benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AskNewInfo {
            get {
                return ResourceManager.GetString("AskNewInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Geri Dön benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklif Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string BidDate {
            get {
                return ResourceManager.GetString("BidDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklif Detayı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string BidDetail {
            get {
                return ResourceManager.GetString("BidDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklif Bilgileri benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string BidInformations {
            get {
                return ResourceManager.GetString("BidInformations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklif No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string BidNo {
            get {
                return ResourceManager.GetString("BidNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklifler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Bids {
            get {
                return ResourceManager.GetString("Bids", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklif Durumu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string BidStatus {
            get {
                return ResourceManager.GetString("BidStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   İptal benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Resmi Değiştir benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ChangePicture {
            get {
                return ResourceManager.GetString("ChangePicture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Değer ve kod uzunluğunu kontrol edin benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CheckValueCode {
            get {
                return ResourceManager.GetString("CheckValueCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kodları benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Codes {
            get {
                return ResourceManager.GetString("Codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tamamlandı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Onay Bekleyen benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ConfirmationPending {
            get {
                return ResourceManager.GetString("ConfirmationPending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yetkili benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teklifi oluşturan kullanıcı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CreateBidUser {
            get {
                return ResourceManager.GetString("CreateBidUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Grup Oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CreateGroup {
            get {
                return ResourceManager.GetString("CreateGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CreateItem {
            get {
                return ResourceManager.GetString("CreateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Soru Oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CreateNewQuestion {
            get {
                return ResourceManager.GetString("CreateNewQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özellik listesi grubu oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CreateSpecSheetGroup {
            get {
                return ResourceManager.GetString("CreateSpecSheetGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Müşteri Adı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Customs {
            get {
                return ResourceManager.GetString("Customs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük Kontrol benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomsControl {
            get {
                return ResourceManager.GetString("CustomsControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük Bilgi İstekleri benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomsRequest {
            get {
                return ResourceManager.GetString("CustomsRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük Bilgi İstek Detayı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomsRequestDetail {
            get {
                return ResourceManager.GetString("CustomsRequestDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tarih benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gün benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sil benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Grubu Sil benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeleteGroup {
            get {
                return ResourceManager.GetString("DeleteGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bu Klasör benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeleteGrpMsg1 {
            get {
                return ResourceManager.GetString("DeleteGrpMsg1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   alt öge içeriyor. Tüm alt öğeler silinecek. benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeleteGrpMsg2 {
            get {
                return ResourceManager.GetString("DeleteGrpMsg2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürünü Sil benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeleteItem {
            get {
                return ResourceManager.GetString("DeleteItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özelliği Sil benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeleteSpecs {
            get {
                return ResourceManager.GetString("DeleteSpecs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gönderim Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sağlama Süresi Hesaplaması benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliveryTimeCalculation {
            get {
                return ResourceManager.GetString("DeliveryTimeCalculation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Açıklama benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Açıklama benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Description1 {
            get {
                return ResourceManager.GetString("Description1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Açıklama 2 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Description2 {
            get {
                return ResourceManager.GetString("Description2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Detay benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Belge No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DocumentNo {
            get {
                return ResourceManager.GetString("DocumentNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Düzenle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özelliği Düzenle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string EditSpecs {
            get {
                return ResourceManager.GetString("EditSpecs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Verilen Siparişler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string EnteredPromisedDate {
            get {
                return ResourceManager.GetString("EnteredPromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Beklenen Tarih benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ExpectedDate {
            get {
                return ResourceManager.GetString("ExpectedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tamamlandı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Finished {
            get {
                return ResourceManager.GetString("Finished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal tanımına git benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string GoToItemDef {
            get {
                return ResourceManager.GetString("GoToItemDef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Silme İşlemi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string hDelete {
            get {
                return ResourceManager.GetString("hDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ev benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün tanımının boş veya yanlış olan son 100 yurtdışı satın alma listesi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iAllCustomsList {
            get {
                return ResourceManager.GetString("iAllCustomsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eksik veya Hatalı Mal Tanımı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iCustomsCard {
            get {
                return ResourceManager.GetString("iCustomsCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   ID benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ID {
            get {
                return ResourceManager.GetString("ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi no ve şifresi kullanılarak giriş yapılacaktır. benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iNewVendorUser {
            get {
                return ResourceManager.GetString("iNewVendorUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi no yanlış yada daha önceden eklendi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iNewVendorUserError {
            get {
                return ResourceManager.GetString("iNewVendorUserError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bilgi eklendi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string InformationAdded {
            get {
                return ResourceManager.GetString("InformationAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bilgi istendi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string InformationRequested {
            get {
                return ResourceManager.GetString("InformationRequested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kaydedilemedi! Lütfen Sonra Tekrar Deneyin benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iNotSaved {
            get {
                return ResourceManager.GetString("iNotSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kaydedildi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iSavedSuccessfully {
            get {
                return ResourceManager.GetString("iSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Item {
            get {
                return ResourceManager.GetString("Item", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal Tanımı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemDefinition {
            get {
                return ResourceManager.GetString("ItemDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Detayı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemDetail {
            get {
                return ResourceManager.GetString("ItemDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Listesi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemList {
            get {
                return ResourceManager.GetString("ItemList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Hazır benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemReady {
            get {
                return ResourceManager.GetString("ItemReady", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürünün Hazır Olduğu Tarih benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemReadyDate {
            get {
                return ResourceManager.GetString("ItemReadyDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürünler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Items {
            get {
                return ResourceManager.GetString("Items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Özellikleri benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemSpecsTab {
            get {
                return ResourceManager.GetString("ItemSpecsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Son Değişiklik Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LastModifiedDate {
            get {
                return ResourceManager.GetString("LastModifiedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Son Degisiklik Kullanıcı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LastModifiedUser {
            get {
                return ResourceManager.GetString("LastModifiedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçiler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ListVendorUser {
            get {
                return ResourceManager.GetString("ListVendorUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Toplama Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LoadingDate {
            get {
                return ResourceManager.GetString("LoadingDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Toplama Tarihi (E kadar) / Taşıma Yöntemi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LoadingDLogisticType {
            get {
                return ResourceManager.GetString("LoadingDLogisticType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   İşlem Dökümü benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Log {
            get {
                return ResourceManager.GetString("Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Giriş Yap benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Lojistik benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Logistic {
            get {
                return ResourceManager.GetString("Logistic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Lojistik Tipi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LogisticType {
            get {
                return ResourceManager.GetString("LogisticType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Çıkış Yap benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Minivan benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Minivan {
            get {
                return ResourceManager.GetString("Minivan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ay benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Month {
            get {
                return ResourceManager.GetString("Month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sorularım benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string MyQuestions {
            get {
                return ResourceManager.GetString("MyQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Adı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özellik Ekle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NewSpecBtn {
            get {
                return ResourceManager.GetString("NewSpecBtn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Kullanıcı Ekle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NewUser {
            get {
                return ResourceManager.GetString("NewUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Numaralı Sipariş benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NumberedOrder {
            get {
                return ResourceManager.GetString("NumberedOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eski Stok No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OldItemNo {
            get {
                return ResourceManager.GetString("OldItemNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Old Item No Yapısı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OldItemNoPattern {
            get {
                return ResourceManager.GetString("OldItemNoPattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Old Item No oluştur benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OldItemNoWizard {
            get {
                return ResourceManager.GetString("OldItemNoWizard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sipariş benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Order {
            get {
                return ResourceManager.GetString("Order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sipariş Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OrderDate {
            get {
                return ResourceManager.GetString("OrderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Siparişler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Şifre benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Pass {
            get {
                return ResourceManager.GetString("Pass", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Şifre benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yol benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Path {
            get {
                return ResourceManager.GetString("Path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Bekleyen Siparişler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PendingPromisedDate {
            get {
                return ResourceManager.GetString("PendingPromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Telefon benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Resim benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Picture {
            get {
                return ResourceManager.GetString("Picture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Uçak benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Plane {
            get {
                return ResourceManager.GetString("Plane", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Adı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PortalName {
            get {
                return ResourceManager.GetString("PortalName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Durumu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PortalStatus {
            get {
                return ResourceManager.GetString("PortalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ön İzleme benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PreviewBtn {
            get {
                return ResourceManager.GetString("PreviewBtn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bir Önceki Termin Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PreviousPromisedDate {
            get {
                return ResourceManager.GetString("PreviousPromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Detay Çıktısı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PrintItemDetail {
            get {
                return ResourceManager.GetString("PrintItemDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Proje Adı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ProjectName {
            get {
                return ResourceManager.GetString("ProjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Proje No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ProjectNo {
            get {
                return ResourceManager.GetString("ProjectNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Taahhüt Edilen Teslim Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PromisedDate {
            get {
                return ResourceManager.GetString("PromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satınalma benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Purchase {
            get {
                return ResourceManager.GetString("Purchase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satınalma Açıklaması benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PurchaseDescription {
            get {
                return ResourceManager.GetString("PurchaseDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satınalma Listesi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PurchaseList {
            get {
                return ResourceManager.GetString("PurchaseList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Miktar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Alış Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ReceiveDate {
            get {
                return ResourceManager.GetString("ReceiveDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kalan Miktar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RemainderQuantity {
            get {
                return ResourceManager.GetString("RemainderQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talep benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Request {
            get {
                return ResourceManager.GetString("Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talebiniz Cevaplandı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestAnswered {
            get {
                return ResourceManager.GetString("RequestAnswered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   İstenilen Tarih benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestedDate {
            get {
                return ResourceManager.GetString("RequestedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talep Eden benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequesterName {
            get {
                return ResourceManager.GetString("RequesterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talebiniz Kapandı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestFinished {
            get {
                return ResourceManager.GetString("RequestFinished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bilgi İste benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestInformation {
            get {
                return ResourceManager.GetString("RequestInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talep Adı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestName {
            get {
                return ResourceManager.GetString("RequestName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talep Gönderildi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestSended {
            get {
                return ResourceManager.GetString("RequestSended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Talebiniz Güncellendi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestUpdated {
            get {
                return ResourceManager.GetString("RequestUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Şifreyi Sıfırla benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ResetPassword {
            get {
                return ResourceManager.GetString("ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Rol benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Role Description benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RoleDescription {
            get {
                return ResourceManager.GetString("RoleDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satış benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Sales {
            get {
                return ResourceManager.GetString("Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satış Kontrolü benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SalesControl {
            get {
                return ResourceManager.GetString("SalesControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satış Detayı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SalesDetail {
            get {
                return ResourceManager.GetString("SalesDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kaydet benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ara benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Seçiniz benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Türkçe benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SelectLanguage {
            get {
                return ResourceManager.GetString("SelectLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Resim Seç benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SelectPicture {
            get {
                return ResourceManager.GetString("SelectPicture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gönder benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Tanım Ayarla benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SetNewDefinition {
            get {
                return ResourceManager.GetString("SetNewDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tümünü Göster benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ShowAll {
            get {
                return ResourceManager.GetString("ShowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özellik Sırası benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SpecOrder {
            get {
                return ResourceManager.GetString("SpecOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özellik Listesi Sırası benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SpecSheetOrder {
            get {
                return ResourceManager.GetString("SpecSheetOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Özellik Listesi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SpecSheetTab {
            get {
                return ResourceManager.GetString("SpecSheetTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Durum benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Stok Kodu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string StockCode {
            get {
                return ResourceManager.GetString("StockCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tablo Görünümü benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string TableView {
            get {
                return ResourceManager.GetString("TableView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teknik Sorular benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string TechnicalQuestions {
            get {
                return ResourceManager.GetString("TechnicalQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Saat benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Toplam benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tır benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Truck {
            get {
                return ResourceManager.GetString("Truck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tutar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Tutation {
            get {
                return ResourceManager.GetString("Tutation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Birim Fiyat benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string UnitPrice {
            get {
                return ResourceManager.GetString("UnitPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   E kadar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Until {
            get {
                return ResourceManager.GetString("Until", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kullanıcı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kullanıcı Detayı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string UserDetail {
            get {
                return ResourceManager.GetString("UserDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kullanıcı ID benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string UserID {
            get {
                return ResourceManager.GetString("UserID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kullanıcılar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kullanıcılar ve Roller benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string UsersAndRoles {
            get {
                return ResourceManager.GetString("UsersAndRoles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Değerleri benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Values {
            get {
                return ResourceManager.GetString("Values", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi Özel Durum Açıklama benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorDesc1 {
            get {
                return ResourceManager.GetString("VendorDesc1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi Özel Durum Açıklama 2 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorDesc2 {
            get {
                return ResourceManager.GetString("VendorDesc2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Firma Filtrele benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorFilter {
            get {
                return ResourceManager.GetString("VendorFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi Bilgileri benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorInformations {
            get {
                return ResourceManager.GetString("VendorInformations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Firma Adı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorName {
            get {
                return ResourceManager.GetString("VendorName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorNo {
            get {
                return ResourceManager.GetString("VendorNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bilgi Bekleniyor benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string WaitingInformation {
            get {
                return ResourceManager.GetString("WaitingInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bekleniyor benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string WaitingLogistic {
            get {
                return ResourceManager.GetString("WaitingLogistic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Giriş Ambarı İşlem Süresi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string WarehouseProcessingTime {
            get {
                return ResourceManager.GetString("WarehouseProcessingTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Haftalık benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Weekly {
            get {
                return ResourceManager.GetString("Weekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Hoş Geldiniz benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Cevapla benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string WriteReply {
            get {
                return ResourceManager.GetString("WriteReply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yıl benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Year {
            get {
                return ResourceManager.GetString("Year", resourceCulture);
            }
        }
    }
}
