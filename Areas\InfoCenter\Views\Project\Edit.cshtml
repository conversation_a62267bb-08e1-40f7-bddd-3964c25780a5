﻿@model Kreon.Data.Zenon_data_Sales_Line

@{
    ViewBag.Title = "Edit";
}

<h2>Edit</h2>


@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Zenon_data_Sales_Line</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.Document_No_)

        <div class="form-group">
            @Html.LabelFor(model => model.Sell_to_Customer_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Sell_to_Customer_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Sell_to_Customer_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Location_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Location_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Location_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Posting_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Posting_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Posting_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipment_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipment_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipment_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Description_2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description_2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Description_2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_of_Measure, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_of_Measure, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_of_Measure, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Quantity, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Quantity, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Quantity, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Outstanding_Quantity, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Outstanding_Quantity, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Outstanding_Quantity, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__to_Invoice, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__to_Invoice, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__to_Invoice, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__to_Ship, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__to_Ship, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__to_Ship, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_Price, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_Price, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_Price, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_Cost__LCY_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_Cost__LCY_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_Cost__LCY_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VAT__, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VAT__, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VAT__, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Line_Discount__, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Line_Discount__, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Line_Discount__, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Line_Discount_Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Line_Discount_Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Line_Discount_Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Amount_Including_VAT, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Amount_Including_VAT, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Amount_Including_VAT, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Allow_Invoice_Disc_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Allow_Invoice_Disc_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Allow_Invoice_Disc_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Gross_Weight, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Gross_Weight, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Gross_Weight, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Net_Weight, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Net_Weight, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Net_Weight, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Units_per_Parcel, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Units_per_Parcel, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Units_per_Parcel, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_Volume, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_Volume, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_Volume, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shortcut_Dimension_1_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shortcut_Dimension_1_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shortcut_Dimension_1_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shortcut_Dimension_2_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shortcut_Dimension_2_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shortcut_Dimension_2_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Customer_Price_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Customer_Price_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Customer_Price_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Job_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Job_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Job_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Phase_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Phase_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Phase_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Task_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Task_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Task_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Step_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Step_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Step_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Job_Applies_to_ID, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Job_Applies_to_ID, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Job_Applies_to_ID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Apply_and_Close__Job_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Apply_and_Close__Job_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Apply_and_Close__Job_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Work_Type_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Work_Type_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Work_Type_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Outstanding_Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Outstanding_Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Outstanding_Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__Shipped_Not_Invoiced, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__Shipped_Not_Invoiced, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__Shipped_Not_Invoiced, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipped_Not_Invoiced, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipped_Not_Invoiced, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipped_Not_Invoiced, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Quantity_Shipped, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Quantity_Shipped, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Quantity_Shipped, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Quantity_Invoiced, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Quantity_Invoiced, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Quantity_Invoiced, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipment_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipment_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipment_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Profit__, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Profit__, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Profit__, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Bill_to_Customer_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Bill_to_Customer_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Bill_to_Customer_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Inv__Discount_Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Inv__Discount_Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Inv__Discount_Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Purchase_Order_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Purchase_Order_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Purchase_Order_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Drop_Shipment, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Drop_Shipment, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Drop_Shipment, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Gen__Bus__Posting_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Gen__Bus__Posting_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Gen__Bus__Posting_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Gen__Prod__Posting_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Gen__Prod__Posting_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Gen__Prod__Posting_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Transaction_Type, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Transaction_Type, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Transaction_Type, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Transport_Method, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Transport_Method, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Transport_Method, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Exit_Point, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Exit_Point, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Exit_Point, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Area, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Area, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Area, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Transaction_Specification, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Transaction_Specification, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Transaction_Specification, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tax_Area_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tax_Area_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tax_Area_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tax_Liable, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tax_Liable, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tax_Liable, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tax_Group_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tax_Group_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tax_Group_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VAT_Bus__Posting_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VAT_Bus__Posting_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VAT_Bus__Posting_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VAT_Prod__Posting_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VAT_Prod__Posting_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VAT_Prod__Posting_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Currency_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Currency_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Currency_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Outstanding_Amount__LCY_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Outstanding_Amount__LCY_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Outstanding_Amount__LCY_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipped_Not_Invoiced__LCY_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipped_Not_Invoiced__LCY_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipped_Not_Invoiced__LCY_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Blanket_Order_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Blanket_Order_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Blanket_Order_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VAT_Base_Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VAT_Base_Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VAT_Base_Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_Cost, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_Cost, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_Cost, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.System_Created_Entry, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.System_Created_Entry, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.System_Created_Entry, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Line_Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Line_Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Line_Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VAT_Difference, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VAT_Difference, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VAT_Difference, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Inv__Disc__Amount_to_Invoice, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Inv__Disc__Amount_to_Invoice, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Inv__Disc__Amount_to_Invoice, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VAT_Identifier, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VAT_Identifier, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VAT_Identifier, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.IC_Partner_Reference, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.IC_Partner_Reference, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.IC_Partner_Reference, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Variant_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Variant_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Variant_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Bin_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Bin_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Bin_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__per_Unit_of_Measure, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__per_Unit_of_Measure, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__per_Unit_of_Measure, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Planned, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Planned, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Planned, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_of_Measure_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_of_Measure_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_of_Measure_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Quantity__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Quantity__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Quantity__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Outstanding_Qty___Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Outstanding_Qty___Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Outstanding_Qty___Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__to_Invoice__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__to_Invoice__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__to_Invoice__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__to_Ship__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__to_Ship__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__to_Ship__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__Shipped_Not_Invd___Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__Shipped_Not_Invd___Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__Shipped_Not_Invd___Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__Shipped__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__Shipped__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__Shipped__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Qty__Invoiced__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Qty__Invoiced__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Qty__Invoiced__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.FA_Posting_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.FA_Posting_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.FA_Posting_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Depreciation_Book_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Depreciation_Book_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Depreciation_Book_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Depr__until_FA_Posting_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Depr__until_FA_Posting_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Depr__until_FA_Posting_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Duplicate_in_Depreciation_Book, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Duplicate_in_Depreciation_Book, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Duplicate_in_Depreciation_Book, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Use_Duplication_List, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Use_Duplication_List, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Use_Duplication_List, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Responsibility_Center, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Responsibility_Center, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Responsibility_Center, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Out_of_Stock_Substitution, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Out_of_Stock_Substitution, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Out_of_Stock_Substitution, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Originally_Ordered_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Originally_Ordered_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Originally_Ordered_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Originally_Ordered_Var__Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Originally_Ordered_Var__Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Originally_Ordered_Var__Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Cross_Reference_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Cross_Reference_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Cross_Reference_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Unit_of_Measure__Cross_Ref__, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Unit_of_Measure__Cross_Ref__, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Unit_of_Measure__Cross_Ref__, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Cross_Reference_Type_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Cross_Reference_Type_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Cross_Reference_Type_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Item_Category_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Item_Category_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Item_Category_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Nonstock, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Nonstock, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Nonstock, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Purchasing_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Purchasing_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Purchasing_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Product_Group_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Product_Group_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Product_Group_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Special_Order, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Special_Order, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Special_Order, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Special_Order_Purchase_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Special_Order_Purchase_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Special_Order_Purchase_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Completely_Shipped, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Completely_Shipped, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Completely_Shipped, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Requested_Delivery_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Requested_Delivery_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Requested_Delivery_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Promised_Delivery_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Promised_Delivery_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Promised_Delivery_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipping_Time, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipping_Time, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipping_Time, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Outbound_Whse__Handling_Time, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Outbound_Whse__Handling_Time, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Outbound_Whse__Handling_Time, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Planned_Delivery_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Planned_Delivery_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Planned_Delivery_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Planned_Shipment_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Planned_Shipment_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Planned_Shipment_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipping_Agent_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipping_Agent_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipping_Agent_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Shipping_Agent_Service_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Shipping_Agent_Service_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Shipping_Agent_Service_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Allow_Item_Charge_Assignment, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Allow_Item_Charge_Assignment, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Allow_Item_Charge_Assignment, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Qty__to_Receive, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Qty__to_Receive, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Qty__to_Receive, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Qty__to_Receive__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Qty__to_Receive__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Qty__to_Receive__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Qty__Rcd__Not_Invd_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Qty__Rcd__Not_Invd_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Qty__Rcd__Not_Invd_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ret__Qty__Rcd__Not_Invd__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ret__Qty__Rcd__Not_Invd__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ret__Qty__Rcd__Not_Invd__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Rcd__Not_Invd_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Rcd__Not_Invd_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Rcd__Not_Invd_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Rcd__Not_Invd___LCY_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Rcd__Not_Invd___LCY_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Rcd__Not_Invd___LCY_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Qty__Received, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Qty__Received, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Qty__Received, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Qty__Received__Base_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Qty__Received__Base_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Qty__Received__Base_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Service_Contract_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Service_Contract_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Service_Contract_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Service_Order_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Service_Order_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Service_Order_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Service_Item_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Service_Item_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Service_Item_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Serv__Price_Adjmt__Gr__Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Serv__Price_Adjmt__Gr__Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Serv__Price_Adjmt__Gr__Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BOM_Item_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BOM_Item_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BOM_Item_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Receipt_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Receipt_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Receipt_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Return_Reason_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Return_Reason_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Return_Reason_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Allow_Line_Disc_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Allow_Line_Disc_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Allow_Line_Disc_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Customer_Disc__Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Customer_Disc__Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Customer_Disc__Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Service_Posting_Date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Service_Posting_Date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Service_Posting_Date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.doviz, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.doviz, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.doviz, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.EUR_Fiyat, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.EUR_Fiyat, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.EUR_Fiyat, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Old_Order_qty_1, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Old_Order_qty_1, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Old_Order_qty_1, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Sales_Unit_Price, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Sales_Unit_Price, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Sales_Unit_Price, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Sales_Currency_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Sales_Currency_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Sales_Currency_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Sales_Line_Amount, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Sales_Line_Amount, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Sales_Line_Amount, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Created_at, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Created_at, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Created_at, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Created_by, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Created_by, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Created_by, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Modified_at, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Modified_at, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Modified_at, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Modified_by, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Modified_by, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Modified_by, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Customer_Order_No_, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Customer_Order_No_, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Customer_Order_No_, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Customer_Order_Matched, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Customer_Order_Matched, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Customer_Order_Matched, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Teklif_Aciklama_Satiri, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Teklif_Aciklama_Satiri, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Teklif_Aciklama_Satiri, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Satis_Temsilcisi_Onay, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Satis_Temsilcisi_Onay, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Satis_Temsilcisi_Onay, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Malzeme_Notlari, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Malzeme_Notlari, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Malzeme_Notlari, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Termin_Tarihi_Verildi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Termin_Tarihi_Verildi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Termin_Tarihi_Verildi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Satis_Temsilcisi_UserID, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Satis_Temsilcisi_UserID, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Satis_Temsilcisi_UserID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Urun_Agaci_Hazirlandi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Urun_Agaci_Hazirlandi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Urun_Agaci_Hazirlandi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.MONTAJ_Notlari, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.MONTAJ_Notlari, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.MONTAJ_Notlari, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Customer_Location_Code, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Customer_Location_Code, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Customer_Location_Code, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.UrgentReqQty, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.UrgentReqQty, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.UrgentReqQty, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CancelledQty, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.CancelledQty, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.CancelledQty, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.OrderStatus, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.OrderStatus, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.OrderStatus, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Planned_Shipment_Date2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Planned_Shipment_Date2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Planned_Shipment_Date2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Saglama_suresi_En_uzun_kod, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Saglama_suresi_En_uzun_kod, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Saglama_suresi_En_uzun_kod, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Saglama_suresi_En_uzun_ad, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Saglama_suresi_En_uzun_ad, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Saglama_suresi_En_uzun_ad, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Saglama_suresi_En_uzun_sure, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Saglama_suresi_En_uzun_sure, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Saglama_suresi_En_uzun_sure, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ShipmentDocNo, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ShipmentDocNo, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ShipmentDocNo, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Onay_Red_Tarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Onay_Red_Tarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Onay_Red_Tarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Red_Aciklamasi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Red_Aciklamasi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Red_Aciklamasi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Red_Neden_Kodu, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Red_Neden_Kodu, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Red_Neden_Kodu, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_1, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_1, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_1, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_1, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_1, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_1, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_1, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_1, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_1, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_1, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_1, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_1, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_3, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_3, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_3, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_3, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_3, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_3, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_3, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_3, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_3, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_3, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_3, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_3, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_4, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_4, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_4, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_4, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_4, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_4, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_4, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_4, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_4, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_4, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_4, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_4, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_5, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_5, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_5, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_5, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_5, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_5, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_5, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_5, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_5, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_5, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_5, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_5, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_6, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_6, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_6, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_6, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_6, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_6, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_6, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_6, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_6, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_6, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_6, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_6, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_7, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_7, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_7, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_7, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_7, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_7, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_7, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_7, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_7, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_7, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_7, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_7, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_8, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_8, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_8, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_8, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_8, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_8, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_8, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_8, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_8, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_8, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_8, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_8, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_9, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_9, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_9, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_9, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_9, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_9, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_9, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_9, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_9, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_9, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_9, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_9, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Eksik_Madde_Kodu_10, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Eksik_Madde_Kodu_10, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Eksik_Madde_Kodu_10, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Tedarikci_Adi_10, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Tedarikci_Adi_10, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Tedarikci_Adi_10, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_10, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_10, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ilk_Acik_SA_Siparis_Tarihi_10, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Taahhut_Tarihi_10, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Taahhut_Tarihi_10, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Taahhut_Tarihi_10, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.MRP_Blocked, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.MRP_Blocked, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.MRP_Blocked, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TruckAcilMiktar, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TruckAcilMiktar, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TruckAcilMiktar, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TruckAcilTarih, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TruckAcilTarih, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TruckAcilTarih, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.AirAcilMiktar, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.AirAcilMiktar, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.AirAcilMiktar, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.AirAcilTarih, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.AirAcilTarih, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.AirAcilTarih, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BirimSuresi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BirimSuresi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BirimSuresi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ToplamSure, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ToplamSure, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ToplamSure, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.KumulatifToplam, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.KumulatifToplam, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.KumulatifToplam, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.EldekiStok, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.EldekiStok, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.EldekiStok, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.IhtiyacMiktar, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.IhtiyacMiktar, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.IhtiyacMiktar, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.KalanSure, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.KalanSure, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.KalanSure, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Sikayet, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Sikayet, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Sikayet, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Promised_Delivery_Date_OLD, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Promised_Delivery_Date_OLD, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Promised_Delivery_Date_OLD, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.SiparisHazir, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.SiparisHazir, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SiparisHazir, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ZenonMiktar, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ZenonMiktar, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ZenonMiktar, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_Fiyati, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_Fiyati, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_Fiyati, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_Fiyati_Kullan, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_Fiyati_Kullan, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_Fiyati_Kullan, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_Alt_Fiyati, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_Alt_Fiyati, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_Alt_Fiyati, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_Alt_Fiyati_Kullan, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_Alt_Fiyati_Kullan, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_Alt_Fiyati_Kullan, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_Ust_Fiyati, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_Ust_Fiyati, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_Ust_Fiyati, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_Ust_Fiyati_Kullan, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_Ust_Fiyati_Kullan, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_Ust_Fiyati_Kullan, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ozel_Fiyat, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ozel_Fiyat, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ozel_Fiyat, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Liste_fiyat_para_birimi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Liste_fiyat_para_birimi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Liste_fiyat_para_birimi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Ozel_Fiyat_Kullan, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Ozel_Fiyat_Kullan, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Ozel_Fiyat_Kullan, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.GTIP_No, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.GTIP_No, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.GTIP_No, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TeknikResmiOlmayanlar, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TeknikResmiOlmayanlar, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TeknikResmiOlmayanlar, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Fiyat_Onay_Tarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Fiyat_Onay_Tarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Fiyat_Onay_Tarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Hes__En_Uzun_Teslim_Tarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Hes__En_Uzun_Teslim_Tarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Hes__En_Uzun_Teslim_Tarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Developer, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Developer, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Developer, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.PnlSvkBEnTahTr, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.PnlSvkBEnTahTr, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.PnlSvkBEnTahTr, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.PnlSvkBEnTahTrBl, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.PnlSvkBEnTahTrBl, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.PnlSvkBEnTahTrBl, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TR_BG, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TR_BG, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TR_BG, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.KReon_NV_Remaining_QTY, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.KReon_NV_Remaining_QTY, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.KReon_NV_Remaining_QTY, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Customer_Order_No__OLD, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Customer_Order_No__OLD, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Customer_Order_No__OLD, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Previous_Requested_date, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Previous_Requested_date, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Previous_Requested_date, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.C749_Stoklu_en_uzun_tarih, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.C749_Stoklu_en_uzun_tarih, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.C749_Stoklu_en_uzun_tarih, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Old_Item2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Old_Item2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Old_Item2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.order_date2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.order_date2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.order_date2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.C749_Stoklu_en_uzun_tarih_Bugun, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.C749_Stoklu_en_uzun_tarih_Bugun, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.C749_Stoklu_en_uzun_tarih_Bugun, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.bir_onceki_749_tarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.bir_onceki_749_tarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.bir_onceki_749_tarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ShipMethod, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ShipMethod, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ShipMethod, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.filtrele, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.filtrele, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.filtrele, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.partial_Production_ok, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.partial_Production_ok, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.partial_Production_ok, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Responsible_TR, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Responsible_TR, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Responsible_TR, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Destination_Country, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Destination_Country, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Destination_Country, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Order_Country, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Order_Country, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Order_Country, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Bom_Approve, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Bom_Approve, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Bom_Approve, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Bom_Approve_date_time, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Bom_Approve_date_time, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Bom_Approve_date_time, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BE_Item_Category, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BE_Item_Category, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BE_Item_Category, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BE_Prod_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BE_Prod_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BE_Prod_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BE_Prod_Sub_Group, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BE_Prod_Sub_Group, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BE_Prod_Sub_Group, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BE_Extended_Text, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BE_Extended_Text, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BE_Extended_Text, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BE_Extended_Text2, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BE_Extended_Text2, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BE_Extended_Text2, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BE_UOM, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BE_UOM, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BE_UOM, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BomAvailDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BomAvailDate, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BomAvailDate, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Save" class="btn btn-default" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>

@section Scripts {
    @Scripts.Render("~/bundles/jqueryval")
}
