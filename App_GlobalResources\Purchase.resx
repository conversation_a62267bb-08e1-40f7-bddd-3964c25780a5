﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Customs" xml:space="preserve">
    <value>Gümrük</value>
    <comment>Purchase</comment>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Gün</value>
    <comment>Purchase</comment>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Gönderim Tarihi</value>
    <comment>Purchase</comment>
  </data>
  <data name="DeliveryTimeCalculation" xml:space="preserve">
    <value>Sağlama Süresi Hesaplaması</value>
    <comment>Purchase</comment>
  </data>
  <data name="Description1" xml:space="preserve">
    <value>Açıklama</value>
    <comment>Purchase</comment>
  </data>
  <data name="Description2" xml:space="preserve">
    <value>Açıklama 2</value>
    <comment>Purchase</comment>
  </data>
  <data name="EnteredPromisedDate" xml:space="preserve">
    <value>Termin Verilen Siparişler</value>
    <comment>Purchase</comment>
  </data>
  <data name="iEnteredListEmpty" xml:space="preserve">
    <value>Termin Tarihi Verilen Siparişiniz Bulunmamaktadır.</value>
    <comment>Purchase</comment>
  </data>
  <data name="iPendingListEmpty" xml:space="preserve">
    <value>Termin Tarihi Bekliyen Siparişiniz Bulunmamaktadır.</value>
    <comment>Purchase</comment>
  </data>
  <data name="LastModifiedDate" xml:space="preserve">
    <value>Portal Son Değişiklik Tarihi</value>
    <comment>Purchase</comment>
  </data>
  <data name="LastModifiedUser" xml:space="preserve">
    <value>Portal Son Degisiklik Kullanıcı</value>
    <comment>Purchase</comment>
  </data>
  <data name="Logistic" xml:space="preserve">
    <value>Lojistik</value>
    <comment>Purchase</comment>
  </data>
  <data name="NumberedOrder" xml:space="preserve">
    <value>Numaralı Sipariş</value>
    <comment>Purchase</comment>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Siparişler</value>
    <comment>Purchase</comment>
  </data>
  <data name="PendingPromisedDate" xml:space="preserve">
    <value>Termin Bekleyen Siparişler</value>
    <comment>Purchase</comment>
  </data>
  <data name="PortalStatus" xml:space="preserve">
    <value>Portal Durumu</value>
    <comment>Purchase</comment>
  </data>
  <data name="PreviousPromisedDate" xml:space="preserve">
    <value>Bir Önceki Termin Tarihi</value>
    <comment>Purchase</comment>
  </data>
  <data name="PromisedDate" xml:space="preserve">
    <value>Termin Tarihi</value>
    <comment>Purchase</comment>
  </data>
  <data name="PurchaseDescription" xml:space="preserve">
    <value>Satınalma Açıklaması</value>
    <comment>Purchase</comment>
  </data>
  <data name="VendorDesc1" xml:space="preserve">
    <value>Tedarikçi Özel Durum Açıklama</value>
    <comment>Purchase</comment>
  </data>
  <data name="VendorDesc2" xml:space="preserve">
    <value>Tedarikçi Özel Durum Açıklama 2</value>
    <comment>Purchase</comment>
  </data>
  <data name="Weekly" xml:space="preserve">
    <value>Haftalık</value>
    <comment>Purchase</comment>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Geri Dön</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="DocumentNo" xml:space="preserve">
    <value>Belge No</value>
    <comment>General</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
    <comment>General</comment>
  </data>
  <data name="OldItemNo" xml:space="preserve">
    <value>Eski Stok No</value>
    <comment>General</comment>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>Sipariş Tarihi</value>
    <comment>General</comment>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Miktar</value>
  </data>
  <data name="ReceiveDate" xml:space="preserve">
    <value>Alış Tarihi</value>
  </data>
  <data name="RemainderQuantity" xml:space="preserve">
    <value>Kalan Miktar</value>
  </data>
  <data name="ReqDeliveryDate" xml:space="preserve">
    <value>İstenilen Gönderim Tarihi</value>
  </data>
  <data name="RequestedDate" xml:space="preserve">
    <value>İstenilen Tarih</value>
    <comment>General</comment>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Gönder</value>
  </data>
  <data name="WarehouseProcessingTime" xml:space="preserve">
    <value>Giriş Ambarı İşlem Süresi</value>
  </data>
  <data name="AdditionalInfo" xml:space="preserve">
    <value>EK BİLGİ</value>
  </data>
  <data name="AnalysisCertificateRequired" xml:space="preserve">
    <value>Analiz Sertifikası Gerekli mi</value>
  </data>
  <data name="ATRRequired" xml:space="preserve">
    <value>ATR Gerekli mi ?</value>
  </data>
  <data name="CERequired" xml:space="preserve">
    <value>CE Belgesi Gerekli mi ?</value>
  </data>
  <data name="CEValidityPeriod" xml:space="preserve">
    <value>CE Belgesi Süre Sonu</value>
  </data>
  <data name="CustomDelivered" xml:space="preserve">
    <value>Gümrükten Çıktı</value>
    <comment>portal status</comment>
  </data>
  <data name="DeliveredLogistics" xml:space="preserve">
    <value>Lojistik Firmasına Gönderildi</value>
    <comment>portal status</comment>
  </data>
  <data name="EURO1Required" xml:space="preserve">
    <value>EURO-1 Gerekli mi?</value>
  </data>
  <data name="GTIP" xml:space="preserve">
    <value>GTIP</value>
  </data>
  <data name="iCustomsEmpty" xml:space="preserve">
    <value>Mal Tanımı alanı boş olan kayıt sayısı</value>
  </data>
  <data name="InCustoms" xml:space="preserve">
    <value>Gümrükte</value>
    <comment>portal status</comment>
  </data>
  <data name="InLogisticCompany" xml:space="preserve">
    <value>Lojistik Firmasında</value>
    <comment>portal status</comment>
  </data>
  <data name="ItemDefinition" xml:space="preserve">
    <value>Mal Tanımı</value>
  </data>
  <data name="ItemNo" xml:space="preserve">
    <value>Madde No</value>
  </data>
  <data name="LogisticsCompanyDelivered" xml:space="preserve">
    <value>Lojistik Firmasından Çıktı</value>
    <comment>portal status</comment>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Üretici Firma</value>
  </data>
  <data name="ManufacturerDeclarationRequired" xml:space="preserve">
    <value>Üretici Beyanı Gerekli mi?</value>
  </data>
  <data name="Origin" xml:space="preserve">
    <value>Menşei</value>
  </data>
  <data name="OriginCertificateNecessary" xml:space="preserve">
    <value>Menşei Şehadetnamesi Gerekli mi</value>
  </data>
  <data name="Other1" xml:space="preserve">
    <value>Varsa Diğer 1</value>
  </data>
  <data name="Other2" xml:space="preserve">
    <value>Varsa Diğer 2</value>
  </data>
  <data name="PromisedDateEntered" xml:space="preserve">
    <value>Termin Verildi</value>
    <comment>portal status</comment>
  </data>
  <data name="PromisedDatePending" xml:space="preserve">
    <value>Termin Veilmesi Bekleniyor</value>
    <comment>portal status</comment>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Seçiniz</value>
  </data>
  <data name="VendorNo" xml:space="preserve">
    <value>Satıcı No</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detay</value>
  </data>
  <data name="VendorExitDate" xml:space="preserve">
    <value>Satıcıdan çıkış tarihi</value>
  </data>
  <data name="DeliveryReceived" xml:space="preserve">
    <value>Teslim Alındı</value>
  </data>
  <data name="DeliverySended" xml:space="preserve">
    <value>Delivery Sended</value>
  </data>
  <data name="DateEmpty" xml:space="preserve">
    <value>Tarih Girilmedi</value>
  </data>
  <data name="PromisedReceiptDate" xml:space="preserve">
    <value>Taahhüt Edilen Teslim Tarihi</value>
  </data>
  <data name="ItemDescription" xml:space="preserve">
    <value>Ürün Açıklaması</value>
  </data>
  <data name="ItemDescription2" xml:space="preserve">
    <value>Ürün Açıklaması 2</value>
  </data>
  <data name="WorkDay" xml:space="preserve">
    <value>İş Günü</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Tarih alanı boş bırakılamaz</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Şifre Değiştir</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Yeni Şifre</value>
  </data>
  <data name="NewPassword2" xml:space="preserve">
    <value>Yeni Şifre (Tekrar)</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Eski Şifre</value>
  </data>
  <data name="UserProfile" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="Pcs" xml:space="preserve">
    <value>Adet</value>
  </data>
  <data name="iPromised" xml:space="preserve">
    <value>Termin Verildi</value>
  </data>
  <data name="iPromisedAlert" xml:space="preserve">
    <value>Daha sonraki bir tarihe termin verildiği icin acıklama girilmesi zorunludur.</value>
  </data>
  <data name="CustomsItemContol" xml:space="preserve">
    <value>Ürün Mal Tanım Kontrolü</value>
  </data>
  <data name="NewItemDefinition" xml:space="preserve">
    <value>Yeni Mal Tanımı</value>
  </data>
  <data name="RequiredForItemDefinition" xml:space="preserve">
    <value>Mal Tanımı İçin Gerekli</value>
  </data>
  <data name="SelectItemDefinition" xml:space="preserve">
    <value>Mal Tanımı Seç</value>
  </data>
  <data name="AnalysisCertificate" xml:space="preserve">
    <value>Analiz Sertifikası</value>
  </data>
  <data name="ATR" xml:space="preserve">
    <value>ATR</value>
  </data>
  <data name="CE" xml:space="preserve">
    <value>CE</value>
  </data>
  <data name="CustomsApproved" xml:space="preserve">
    <value>Gümrük Onayladı</value>
  </data>
  <data name="Euro1" xml:space="preserve">
    <value>Euro1</value>
  </data>
  <data name="Files" xml:space="preserve">
    <value>Dosyalar</value>
  </data>
  <data name="ItemStatus" xml:space="preserve">
    <value>Ürünün Durumu</value>
  </data>
  <data name="KreonApproved" xml:space="preserve">
    <value>Kreon Onayladı</value>
  </data>
  <data name="ManufacturerDeclaration" xml:space="preserve">
    <value>Uretici Beyanı</value>
  </data>
  <data name="NewItems" xml:space="preserve">
    <value>Yeni Ürünler</value>
  </data>
  <data name="OldItems" xml:space="preserve">
    <value>Eski Ürünler</value>
  </data>
  <data name="OriginCertificate" xml:space="preserve">
    <value>Menşei Şehadetnamesi</value>
  </data>
  <data name="Picture" xml:space="preserve">
    <value>Resim</value>
  </data>
  <data name="VendorStandDelDate" xml:space="preserve">
    <value>Tedarikçi Standart Teslim Tarihi</value>
  </data>
  <data name="PromisedDateNotSaved" xml:space="preserve">
    <value>Termin Verilemedi</value>
  </data>
  <data name="MissingDoc" xml:space="preserve">
    <value>Eksik Dökümanlar</value>
  </data>
  <data name="EditItemDefinition" xml:space="preserve">
    <value>Mal Tanımını düzenle</value>
  </data>
  <data name="MissingCE" xml:space="preserve">
    <value>Eksik CE</value>
  </data>
  <data name="MissingPictures" xml:space="preserve">
    <value>Eksik Resimliler</value>
  </data>
  <data name="RedLines" xml:space="preserve">
    <value>Kırmızı Satırlar</value>
  </data>
  <data name="Unrequsted" xml:space="preserve">
    <value>Bilgi İstenmemiş</value>
    <comment>Unrequested</comment>
  </data>
  <data name="Revision" xml:space="preserve">
    <value>Revizyon</value>
  </data>
  <data name="IsFolderExists" xml:space="preserve">
    <value>Klasör Durumu</value>
  </data>
  <data name="IsPhotoExists" xml:space="preserve">
    <value>Fotoğraf Durumu</value>
  </data>
</root>