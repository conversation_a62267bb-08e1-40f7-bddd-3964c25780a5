﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Kreon.Areas.Chatting.Models;
using Kreon.Controllers;
using Kreon.Data;
using Kreon.Entities;

namespace Kreon.Areas.Chatting.Controllers
{
    public class CategoryController : BaseController
    {

        private List<DropItem> DropItemKonuTipleriGetir(int tip1, int tip2)
        {
            var liste = new List<DropItem>();

            string spDurum = "";
            string projeDurum = "";
            string itemSurum = "";
            string sipDurum = "";

            if (tip1==1)
            {
                //sp
                if (tip2== tip2)
                {
                    // sp
                    spDurum = LangShortDef == "en" ? "Sp About" : "Sp Hakkında";

                }
                if (tip2 == tip2)
                {
                    //proje
                    projeDurum = LangShortDef == "en" ? "Sp-Project" : "Sp-Proje";

                }
                if (tip2 == tip2)
                {
                    //item
                    itemSurum = LangShortDef == "en" ? "Sp-Item" : "Sp-Item";

                }
                if (tip2 == tip2)
                {
                    //siparis
                    sipDurum = LangShortDef == "en" ? "Sp-Order" : "Sp-Sipariş";

                }
            }


            if (tip1 == 2)
            {
                //project
                if (tip2 == tip2)
                {
                    // sp
                    spDurum = LangShortDef == "en" ? "Project-Sp" : "Proje-Sp";

                }
                if (tip2 == tip2)
                {
                    //proje
                    projeDurum = LangShortDef == "en" ? "Project About" : "Proje Hakkında";

                }
                if (tip2 == tip2)
                {
                    //item
                    itemSurum = LangShortDef == "en" ? "Project-Item" : "Proje-Item";

                }
                if (tip2 == tip2)
                {
                    //siparis
                    sipDurum = LangShortDef == "en" ? "Project-Order" : "Proje-Sipariş";

                }
            }
            if (tip1 == 3)
            {
                //item
                if (tip2 == tip2)
                {
                    // sp
                    spDurum = LangShortDef == "en" ? "Item-Sp" : "Item-Sp";

                }
                if (tip2 == tip2)
                {
                    //proje
                    projeDurum = LangShortDef == "en" ? "Item-Project" : "Item-Proje";

                }
                if (tip2 == tip2)
                {
                    //item
                    itemSurum = LangShortDef == "en" ? "Item About" : "Item Hakkında";

                }
                if (tip2 == tip2)
                {
                    //siparis
                    sipDurum = LangShortDef == "en" ? "Item-Order" : "Item-Sipariş";

                }
            }

            if (tip1 == 4)
            {
                //sipariş
                if (tip2 == tip2)
                {
                    // sp
                    spDurum = LangShortDef == "en" ? "Order-Sp" : "Sipariş-Sp";

                }
                if (tip2 == tip2)
                {
                    //proje
                    projeDurum = LangShortDef == "en" ? "Order-Project" : "Sipariş-Proje";

                }
                if (tip2 == tip2)
                {
                    //item
                    itemSurum = LangShortDef == "en" ? "Order-Item" : "Sipariş-Item";

                }
                if (tip2 == tip2)
                {
                    //siparis
                    sipDurum = LangShortDef == "en" ? " About About" : "Sipariş Hakkında";

                }
            }


            liste.Add(new DropItem(){Tanim = spDurum, Id = "1"});
            liste.Add(new DropItem() { Tanim = projeDurum, Id = "2" });
            liste.Add(new DropItem() { Tanim = itemSurum, Id = "3" });
            liste.Add(new DropItem() { Tanim = sipDurum, Id = "4" });
            return liste;
        }
        // GET: Chatting/Category
        public ActionResult Preparing(int katTip = 0, string spNo = "", string projeNo = "", string itemNo = "", string sipNo = "", int sipLineNo = 0)
        {
            bool EklensinMi = true;

            int id = 0;
            if (katTip == 1)
            {
                // spline
                EklensinMi =
                    !_db.Zenon_data_PeraPortalKonusmaKategori.Any(a => a.KategoriTipi == katTip && a.SpNo == spNo);
            }
            if (katTip == 2)
            {
                // project
                EklensinMi =
                    !_db.Zenon_data_PeraPortalKonusmaKategori.Any(a => a.KategoriTipi == katTip && a.ProjeNo == projeNo);
            }
            if (katTip == 3)
            {
                // item
                EklensinMi =
                    !_db.Zenon_data_PeraPortalKonusmaKategori.Any(a => a.KategoriTipi == katTip && a.ItemNo == itemNo);
            }
            if (katTip == 4)
            {
                // sipariş
                EklensinMi =
                    !_db.Zenon_data_PeraPortalKonusmaKategori.Any(a => a.KategoriTipi == katTip && a.SiparisNo == sipNo && a.SiparisLineNo == sipLineNo);
            }

            if (EklensinMi)
            {
                var yeniItem = new Zenon_data_PeraPortalKonusmaKategori()
                {
                    KategoriTipi = katTip,
                    SpNo = sipNo,
                    ProjeNo = projeNo,
                    SiparisNo = sipNo,
                    ItemNo = itemNo,
                    SiparisLineNo = sipLineNo,
                    SalesLineNo = "",
                    SalesHeaderNo = "",
                    KategoriAciklama = "",
                    OldItemNo = ""
                 
                };
                _db.Zenon_data_PeraPortalKonusmaKategori.Add(yeniItem);
                _db.SaveChanges();
                id = yeniItem.KategoriId;

            }
            else
            {
                id = _db.Zenon_data_PeraPortalKonusmaKategori.First(a =>
                     a.KategoriTipi == katTip &&
                     a.SpNo == sipNo &&
                     a.ProjeNo == projeNo &&
                     a.SiparisNo == sipNo &&
                     a.ItemNo == itemNo &&
                     a.SalesLineNo == sipNo &&

                     a.SiparisLineNo == sipLineNo
                 ).KategoriId;
            }

            return RedirectToAction("SubjectList", "Category", new {id = id});
        }

        public ActionResult SubjectList(int id, int tip=0)
        {
            var model = new ChattingSubjectModel();
            var konusmaKategori = _db.Zenon_data_PeraPortalKonusmaKategori.Find(id);


            var katTipi1 = konusmaKategori.KategoriTipi;
            var katTipi2 = tip == 0 ? katTipi1 : tip;

            var katId1 = id;
            var katId2 = 0;
            var konular = new List<Zenon_data_PeraPortalKonusmaKonu>();
            if (katTipi1==katTipi2)
            {
                konular = _db.Zenon_data_PeraPortalKonusmaKonu.Where(a => a.KategoriId1 == id && a.KategoriTip1 == katTipi1 && a.KategoriTip2 == katTipi2).ToList();
            }
            else
            {
                var konusmaListe1 = _db.Zenon_data_PeraPortalKonusmaKonu.Where(a => a.KategoriId1 == id && a.KategoriTip1 == katTipi1 && a.KategoriTip2 == katTipi2).ToList();
                var konusmaListe2 = _db.Zenon_data_PeraPortalKonusmaKonu.Where(a => 
                    a.KategoriTip1 == katTipi2 && 
                    a.KategoriTip2 == katTipi1 &&
                    a.KategoriId2 == id
                    ).ToList();
                konular.AddRange(konusmaListe1);
                konular.AddRange(konusmaListe2);

            }

            var yeniKonu = new Zenon_data_PeraPortalKonusmaKonu()
            {
                KategoriId1 = id,
                KategoriTip1 = katTipi1,
                KategoriTip2 = katTipi2
            };

            model.Kategori1 = konusmaKategori;
            model.Tip = katTipi2;
            model.Konular = konular;

            model.DropItemKonuTipleri = DropItemKonuTipleriGetir(katTipi1, katTipi2);
            return View(model);


        }
    }
}