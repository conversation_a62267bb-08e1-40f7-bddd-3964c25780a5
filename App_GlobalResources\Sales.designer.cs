//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Sales {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Sales() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Sales", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklifi onayla.
        /// </summary>
        internal static string AcceptBid {
            get {
                return ResourceManager.GetString("AcceptBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekle.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Ekle.
        /// </summary>
        internal static string AddItem {
            get {
                return ResourceManager.GetString("AddItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form kullanarak ürün ekle.
        /// </summary>
        internal static string AddItemUseForm {
            get {
                return ResourceManager.GetString("AddItemUseForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Dosya Ekle.
        /// </summary>
        internal static string AddNewFile {
            get {
                return ResourceManager.GetString("AddNewFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Tarihi.
        /// </summary>
        internal static string BidDate {
            get {
                return ResourceManager.GetString("BidDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Detayı.
        /// </summary>
        internal static string BidDetail {
            get {
                return ResourceManager.GetString("BidDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Bilgileri.
        /// </summary>
        internal static string BidInformations {
            get {
                return ResourceManager.GetString("BidInformations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif No.
        /// </summary>
        internal static string BidNo {
            get {
                return ResourceManager.GetString("BidNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklifler.
        /// </summary>
        internal static string Bids {
            get {
                return ResourceManager.GetString("Bids", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Durumu.
        /// </summary>
        internal static string BidStatus {
            get {
                return ResourceManager.GetString("BidStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklifi Oluşturan Kullanıcı.
        /// </summary>
        internal static string BidsUser {
            get {
                return ResourceManager.GetString("BidsUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oluştur.
        /// </summary>
        internal static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Oluştur.
        /// </summary>
        internal static string CreateBid {
            get {
                return ResourceManager.GetString("CreateBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Müşteri Adı.
        /// </summary>
        internal static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sil.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklifi Sil.
        /// </summary>
        internal static string DeleteBid {
            get {
                return ResourceManager.GetString("DeleteBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosyayı Sil.
        /// </summary>
        internal static string DeleteFile {
            get {
                return ResourceManager.GetString("DeleteFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tüm Dosyaları İndir.
        /// </summary>
        internal static string DownloadAllFiles {
            get {
                return ResourceManager.GetString("DownloadAllFiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosyayı İndir.
        /// </summary>
        internal static string DownloadFile {
            get {
                return ResourceManager.GetString("DownloadFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taslak.
        /// </summary>
        internal static string Draft {
            get {
                return ResourceManager.GetString("Draft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taslaklar.
        /// </summary>
        internal static string Drafts {
            get {
                return ResourceManager.GetString("Drafts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosyalar.
        /// </summary>
        internal static string Files {
            get {
                return ResourceManager.GetString("Files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taslaklara  Git.
        /// </summary>
        internal static string GoToDrafts {
            get {
                return ResourceManager.GetString("GoToDrafts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Numaralı Teklifi silmek istediğinizden eminmisiniz ?.
        /// </summary>
        internal static string iDeleteBid {
            get {
                return ResourceManager.GetString("iDeleteBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taslaklarda Bekleyen öğerniz var.
        /// </summary>
        internal static string iDraftsInfo {
            get {
                return ResourceManager.GetString("iDraftsInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilgi.
        /// </summary>
        internal static string Information {
            get {
                return ResourceManager.GetString("Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taslakta teklifiniz bulunmamaktadır..
        /// </summary>
        internal static string iNoBidsInDrafts {
            get {
                return ResourceManager.GetString("iNoBidsInDrafts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaydedilemedi! Lütfen Sonra Tekrar Deneyin.
        /// </summary>
        internal static string iNotSaved {
            get {
                return ResourceManager.GetString("iNotSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaydedildi.
        /// </summary>
        internal static string iSavedSuccessfully {
            get {
                return ResourceManager.GetString("iSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Listeye Eklendi.
        /// </summary>
        internal static string ItemAddedList {
            get {
                return ResourceManager.GetString("ItemAddedList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Açıklaması.
        /// </summary>
        internal static string ItemDescription {
            get {
                return ResourceManager.GetString("ItemDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Detay Soruları.
        /// </summary>
        internal static string ItemDetailQuestions {
            get {
                return ResourceManager.GetString("ItemDetailQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Detayı.
        /// </summary>
        internal static string ItemDetails {
            get {
                return ResourceManager.GetString("ItemDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Bilgileri.
        /// </summary>
        internal static string ItemInformations {
            get {
                return ResourceManager.GetString("ItemInformations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Listesi.
        /// </summary>
        internal static string ItemList {
            get {
                return ResourceManager.GetString("ItemList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Notu.
        /// </summary>
        internal static string ItemNote {
            get {
                return ResourceManager.GetString("ItemNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Adet Bilgisi.
        /// </summary>
        internal static string ItemQuantityInformation {
            get {
                return ResourceManager.GetString("ItemQuantityInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürünler.
        /// </summary>
        internal static string Items {
            get {
                return ResourceManager.GetString("Items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son Değiştirme.
        /// </summary>
        internal static string LastModify {
            get {
                return ResourceManager.GetString("LastModify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sıra No.
        /// </summary>
        internal static string LineNo {
            get {
                return ResourceManager.GetString("LineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log.
        /// </summary>
        internal static string Log {
            get {
                return ResourceManager.GetString("Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to no.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not.
        /// </summary>
        internal static string Note {
            get {
                return ResourceManager.GetString("Note", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Detay Çıktısı.
        /// </summary>
        internal static string PrintItemDetails {
            get {
                return ResourceManager.GetString("PrintItemDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yazdır.
        /// </summary>
        internal static string PrintQuestions {
            get {
                return ResourceManager.GetString("PrintQuestions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proje Adı.
        /// </summary>
        internal static string ProjectName {
            get {
                return ResourceManager.GetString("ProjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proje No.
        /// </summary>
        internal static string ProjectNo {
            get {
                return ResourceManager.GetString("ProjectNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adet.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satış Birimi.
        /// </summary>
        internal static string SalesUnit {
            get {
                return ResourceManager.GetString("SalesUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaydet.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçiniz.
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Müşteri Seçimi.
        /// </summary>
        internal static string SelectCustomer {
            get {
                return ResourceManager.GetString("SelectCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün listesinde olan bir ürünü ekle.
        /// </summary>
        internal static string SelectItemInItemList {
            get {
                return ResourceManager.GetString("SelectItemInItemList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daha önceki tekliflerden ürün ekle.
        /// </summary>
        internal static string SelectItemPreviousBids {
            get {
                return ResourceManager.GetString("SelectItemPreviousBids", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklifi Gönder.
        /// </summary>
        internal static string SendBid {
            get {
                return ResourceManager.GetString("SendBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stok Kodu.
        /// </summary>
        internal static string StockCode {
            get {
                return ResourceManager.GetString("StockCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stok Adı.
        /// </summary>
        internal static string StockName {
            get {
                return ResourceManager.GetString("StockName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toplam.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tutar.
        /// </summary>
        internal static string Tutation {
            get {
                return ResourceManager.GetString("Tutation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Fiyat.
        /// </summary>
        internal static string UnitPrice {
            get {
                return ResourceManager.GetString("UnitPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Bilgilerini Güncelle.
        /// </summary>
        internal static string UpdateBidInformation {
            get {
                return ResourceManager.GetString("UpdateBidInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenen Dosyalar.
        /// </summary>
        internal static string UploadedFiles {
            get {
                return ResourceManager.GetString("UploadedFiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosyayı Yükle.
        /// </summary>
        internal static string UploadFile {
            get {
                return ResourceManager.GetString("UploadFile", resourceCulture);
            }
        }
    }
}
