﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="Customs" xml:space="preserve">
    <value>Customs</value>
    <comment>Purchase</comment>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Day</value>
    <comment>Purchase</comment>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="DeliveryTimeCalculation" xml:space="preserve">
    <value>Delivery Time Calculation</value>
    <comment>Purchase</comment>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Description1" xml:space="preserve">
    <value>Description 1</value>
    <comment>Purchase</comment>
  </data>
  <data name="Description2" xml:space="preserve">
    <value>Description 2</value>
    <comment>Purchase</comment>
  </data>
  <data name="DocumentNo" xml:space="preserve">
    <value>Document No</value>
    <comment>General</comment>
  </data>
  <data name="EnteredPromisedDate" xml:space="preserve">
    <value>Entered Promised Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="iEnteredListEmpty" xml:space="preserve">
    <value>List Empty</value>
    <comment>Purchase</comment>
  </data>
  <data name="iPendingListEmpty" xml:space="preserve">
    <value>List Empty</value>
    <comment>Purchase</comment>
  </data>
  <data name="LastModifiedDate" xml:space="preserve">
    <value>Last Modified Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="LastModifiedUser" xml:space="preserve">
    <value>Last Modified User</value>
    <comment>Purchase</comment>
  </data>
  <data name="Logistic" xml:space="preserve">
    <value>Logistic</value>
    <comment>Purchase</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
    <comment>General</comment>
  </data>
  <data name="NumberedOrder" xml:space="preserve">
    <value>Numbered Order</value>
    <comment>Purchase</comment>
  </data>
  <data name="OldItemNo" xml:space="preserve">
    <value>Old Item No</value>
    <comment>General</comment>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>Order Date</value>
    <comment>General</comment>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Orders</value>
    <comment>Purchase</comment>
  </data>
  <data name="PendingPromisedDate" xml:space="preserve">
    <value>Pending Promised Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="PortalStatus" xml:space="preserve">
    <value>Portal Status</value>
    <comment>Purchase</comment>
  </data>
  <data name="PreviousPromisedDate" xml:space="preserve">
    <value>Previous PromisedDate</value>
    <comment>Purchase</comment>
  </data>
  <data name="PromisedDate" xml:space="preserve">
    <value>Promised Date</value>
    <comment>Purchase</comment>
  </data>
  <data name="PurchaseDescription" xml:space="preserve">
    <value>Purchase Description</value>
    <comment>Purchase</comment>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="ReceiveDate" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="RemainderQuantity" xml:space="preserve">
    <value>Remainder Quantity</value>
  </data>
  <data name="ReqDeliveryDate" xml:space="preserve">
    <value>Requested Delivery Date</value>
  </data>
  <data name="RequestedDate" xml:space="preserve">
    <value>Requested Date</value>
    <comment>General</comment>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="VendorDesc1" xml:space="preserve">
    <value>Vendor Description 1</value>
    <comment>Purchase</comment>
  </data>
  <data name="VendorDesc2" xml:space="preserve">
    <value>Vendor Description 2</value>
    <comment>Purchase</comment>
  </data>
  <data name="WarehouseProcessingTime" xml:space="preserve">
    <value>Warehouse Processing Time</value>
  </data>
  <data name="Weekly" xml:space="preserve">
    <value>Weekly</value>
    <comment>Purchase</comment>
  </data>
  <data name="AdditionalInfo" xml:space="preserve">
    <value>Additional Info</value>
  </data>
  <data name="AnalysisCertificateRequired" xml:space="preserve">
    <value>Analysis Certificate Required ?</value>
  </data>
  <data name="ATRRequired" xml:space="preserve">
    <value>ATR Required ?</value>
  </data>
  <data name="CERequired" xml:space="preserve">
    <value>CE Required ?</value>
  </data>
  <data name="CEValidityPeriod" xml:space="preserve">
    <value>CE Validity Period</value>
  </data>
  <data name="CustomDelivered" xml:space="preserve">
    <value>Custom Delivered</value>
    <comment>portal status</comment>
  </data>
  <data name="DeliveredLogistics" xml:space="preserve">
    <value>Delivered to Logistics Company</value>
    <comment>portal status</comment>
  </data>
  <data name="EURO1Required" xml:space="preserve">
    <value>EURO-1 Required ?</value>
  </data>
  <data name="GTIP" xml:space="preserve">
    <value>GTIP</value>
  </data>
  <data name="iCustomsEmpty" xml:space="preserve">
    <value>The lines of where Item Definition area are blank</value>
  </data>
  <data name="InCustoms" xml:space="preserve">
    <value>In Customs</value>
    <comment>portal status</comment>
  </data>
  <data name="InLogisticCompany" xml:space="preserve">
    <value>In Logistic Company</value>
    <comment>portal status</comment>
  </data>
  <data name="ItemDefinition" xml:space="preserve">
    <value>Item Definition</value>
  </data>
  <data name="ItemNo" xml:space="preserve">
    <value>Item No</value>
  </data>
  <data name="LogisticsCompanyDelivered" xml:space="preserve">
    <value>Logistics Company Delivered</value>
    <comment>portal status</comment>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="ManufacturerDeclarationRequired" xml:space="preserve">
    <value>Manufacturer Declaration Required ?</value>
  </data>
  <data name="Origin" xml:space="preserve">
    <value>Origin</value>
  </data>
  <data name="OriginCertificateNecessary" xml:space="preserve">
    <value>Origin Certificate Necessary ?</value>
  </data>
  <data name="Other1" xml:space="preserve">
    <value>Other1</value>
  </data>
  <data name="Other2" xml:space="preserve">
    <value>Other2</value>
  </data>
  <data name="PromisedDateEntered" xml:space="preserve">
    <value>Promised date is entered</value>
    <comment>portal status</comment>
  </data>
  <data name="PromisedDatePending" xml:space="preserve">
    <value>Promised date is pending</value>
    <comment>portal status</comment>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="VendorNo" xml:space="preserve">
    <value>Vendor No</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="VendorExitDate" xml:space="preserve">
    <value>Vendor Exit Date</value>
  </data>
  <data name="DeliveryReceived" xml:space="preserve">
    <value>Delivery Received</value>
  </data>
  <data name="DeliverySended" xml:space="preserve">
    <value>Delivery Sended</value>
  </data>
  <data name="DateEmpty" xml:space="preserve">
    <value>Date is Empty</value>
  </data>
  <data name="PromisedReceiptDate" xml:space="preserve">
    <value>Promised Delivery Date</value>
  </data>
  <data name="ItemDescription" xml:space="preserve">
    <value>Item Description</value>
  </data>
  <data name="ItemDescription2" xml:space="preserve">
    <value>Item Description 2</value>
  </data>
  <data name="WorkDay" xml:space="preserve">
    <value>Working Day</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Date is required</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="NewPassword2" xml:space="preserve">
    <value>New Password (Again)</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Old Password</value>
  </data>
  <data name="UserProfile" xml:space="preserve">
    <value>User Profile</value>
  </data>
  <data name="Pcs" xml:space="preserve">
    <value>pcs</value>
  </data>
  <data name="iPromised" xml:space="preserve">
    <value>Promised Date Saved</value>
  </data>
  <data name="iPromisedAlert" xml:space="preserve">
    <value>Please enter a description for this date</value>
  </data>
  <data name="CustomsItemContol" xml:space="preserve">
    <value>Item Definition Control</value>
  </data>
  <data name="NewItemDefinition" xml:space="preserve">
    <value>New Item Definition</value>
  </data>
  <data name="RequiredForItemDefinition" xml:space="preserve">
    <value>Required For Item Definition</value>
  </data>
  <data name="SelectItemDefinition" xml:space="preserve">
    <value>Select Item Definition</value>
  </data>
  <data name="AnalysisCertificate" xml:space="preserve">
    <value>Analysis Certificate</value>
  </data>
  <data name="ATR" xml:space="preserve">
    <value>ATR</value>
  </data>
  <data name="CE" xml:space="preserve">
    <value>CE</value>
  </data>
  <data name="CustomsApproved" xml:space="preserve">
    <value>Customs Approved</value>
  </data>
  <data name="Euro1" xml:space="preserve">
    <value>Euro 1</value>
  </data>
  <data name="Files" xml:space="preserve">
    <value>Files</value>
  </data>
  <data name="ItemStatus" xml:space="preserve">
    <value>Item Status</value>
  </data>
  <data name="KreonApproved" xml:space="preserve">
    <value>Kreon Approved</value>
  </data>
  <data name="ManufacturerDeclaration" xml:space="preserve">
    <value>Manufacturer Declaration</value>
  </data>
  <data name="NewItems" xml:space="preserve">
    <value>New Items</value>
  </data>
  <data name="OldItems" xml:space="preserve">
    <value>Old Items</value>
  </data>
  <data name="OriginCertificate" xml:space="preserve">
    <value>Origin Certificate</value>
  </data>
  <data name="Picture" xml:space="preserve">
    <value>Picture</value>
  </data>
  <data name="VendorStandDelDate" xml:space="preserve">
    <value>Vendor Standart Delivery Date</value>
  </data>
  <data name="PromisedDateNotSaved" xml:space="preserve">
    <value>Promised date is not saved</value>
  </data>
  <data name="MissingDoc" xml:space="preserve">
    <value>Missing Documents</value>
  </data>
  <data name="EditItemDefinition" xml:space="preserve">
    <value>Edit Item Definition</value>
  </data>
  <data name="MissingCE" xml:space="preserve">
    <value>Missing CE</value>
  </data>
  <data name="MissingPictures" xml:space="preserve">
    <value>Missing Pictures</value>
  </data>
  <data name="RedLines" xml:space="preserve">
    <value>Red Lines</value>
  </data>
  <data name="Unrequsted" xml:space="preserve">
    <value>Unrequested Items</value>
    <comment>Unrequsted</comment>
  </data>
  <data name="Revision" xml:space="preserve">
    <value>Revision</value>
  </data>
  <data name="IsFolderExists" xml:space="preserve">
    <value>Folder Info</value>
  </data>
  <data name="IsPhotoExists" xml:space="preserve">
    <value>Photo Info</value>
  </data>
</root>