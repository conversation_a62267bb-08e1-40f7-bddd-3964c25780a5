//------------------------------------------------------------------------------
// <auto-generated>
//     Bu kod araç tarafından oluşturuldu.
//     Çalışma Zamanı Sürümü:4.0.30319.42000
//
//     Bu dosyada yapılacak değişiklikler yanlış davranışa neden olabilir ve
//     kod yeniden oluşturulursa kaybolur.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   Yerelleştirilmiş dizeleri aramak gibi işlemler için, tü<PERSON><PERSON> kesin olarak belirtilmiş kaynak sınıfı.
    /// </summary>
    // Bu sınıf StronglyTypedResourceBuilder tarafından otomatik olarak oluşturuldu
    // sınıfı tarafından otomatik olarak oluşturuldu.
    // Üye eklemek veya kaldırmak için .ResX dosyanızı düzenleyin ve sonra da ResGen
    // /str seçeneği ile veya Visual Studio projesini yeniden kur.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Purchase {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Purchase() {
        }
        
        /// <summary>
        ///   Bu sınıf tarafından kullanılan, önbelleğe alınmış ResourceManager örneğini döndürür.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Purchase", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Bu türü kesin olarak belirtilmiş kaynak sınıfını kullanarak geçerli iş parçacığının CurrentUICulture 
        ///   özelliğini tüm kaynak aramaları için geçersiz kılar.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   EK BİLGİ benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AdditionalInfo {
            get {
                return ResourceManager.GetString("AdditionalInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Analiz Sertifikası benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AnalysisCertificate {
            get {
                return ResourceManager.GetString("AnalysisCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Analiz Sertifikası Gerekli mi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string AnalysisCertificateRequired {
            get {
                return ResourceManager.GetString("AnalysisCertificateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   ATR benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ATR {
            get {
                return ResourceManager.GetString("ATR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   ATR Gerekli mi ? benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ATRRequired {
            get {
                return ResourceManager.GetString("ATRRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Geri Dön benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   CE benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CE {
            get {
                return ResourceManager.GetString("CE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   CE Belgesi Gerekli mi ? benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CERequired {
            get {
                return ResourceManager.GetString("CERequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   CE Belgesi Süre Sonu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CEValidityPeriod {
            get {
                return ResourceManager.GetString("CEValidityPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Şifre Değiştir benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrükten Çıktı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomDelivered {
            get {
                return ResourceManager.GetString("CustomDelivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Customs {
            get {
                return ResourceManager.GetString("Customs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrük Onayladı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomsApproved {
            get {
                return ResourceManager.GetString("CustomsApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Mal Tanım Kontrolü benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string CustomsItemContol {
            get {
                return ResourceManager.GetString("CustomsItemContol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tarih Girilmedi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DateEmpty {
            get {
                return ResourceManager.GetString("DateEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tarih alanı boş bırakılamaz benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DateRequired {
            get {
                return ResourceManager.GetString("DateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gün benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Lojistik Firmasına Gönderildi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliveredLogistics {
            get {
                return ResourceManager.GetString("DeliveredLogistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gönderim Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Teslim Alındı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliveryReceived {
            get {
                return ResourceManager.GetString("DeliveryReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Delivery Sended benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliverySended {
            get {
                return ResourceManager.GetString("DeliverySended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sağlama Süresi Hesaplaması benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DeliveryTimeCalculation {
            get {
                return ResourceManager.GetString("DeliveryTimeCalculation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Açıklama benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Açıklama benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Description1 {
            get {
                return ResourceManager.GetString("Description1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Açıklama 2 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Description2 {
            get {
                return ResourceManager.GetString("Description2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Detay benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Belge No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string DocumentNo {
            get {
                return ResourceManager.GetString("DocumentNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal Tanımını düzenle benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string EditItemDefinition {
            get {
                return ResourceManager.GetString("EditItemDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Verilen Siparişler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string EnteredPromisedDate {
            get {
                return ResourceManager.GetString("EnteredPromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Euro1 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Euro1 {
            get {
                return ResourceManager.GetString("Euro1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   EURO-1 Gerekli mi? benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string EURO1Required {
            get {
                return ResourceManager.GetString("EURO1Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Dosyalar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Files {
            get {
                return ResourceManager.GetString("Files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   GTIP benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string GTIP {
            get {
                return ResourceManager.GetString("GTIP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal Tanımı alanı boş olan kayıt sayısı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iCustomsEmpty {
            get {
                return ResourceManager.GetString("iCustomsEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Tarihi Verilen Siparişiniz Bulunmamaktadır. benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iEnteredListEmpty {
            get {
                return ResourceManager.GetString("iEnteredListEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gümrükte benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string InCustoms {
            get {
                return ResourceManager.GetString("InCustoms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Lojistik Firmasında benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string InLogisticCompany {
            get {
                return ResourceManager.GetString("InLogisticCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Tarihi Bekliyen Siparişiniz Bulunmamaktadır. benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iPendingListEmpty {
            get {
                return ResourceManager.GetString("iPendingListEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Verildi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iPromised {
            get {
                return ResourceManager.GetString("iPromised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Daha sonraki bir tarihe termin verildiği icin acıklama girilmesi zorunludur. benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string iPromisedAlert {
            get {
                return ResourceManager.GetString("iPromisedAlert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Klasör Durumu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string IsFolderExists {
            get {
                return ResourceManager.GetString("IsFolderExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Fotoğraf Durumu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string IsPhotoExists {
            get {
                return ResourceManager.GetString("IsPhotoExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal Tanımı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemDefinition {
            get {
                return ResourceManager.GetString("ItemDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Açıklaması benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemDescription {
            get {
                return ResourceManager.GetString("ItemDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürün Açıklaması 2 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemDescription2 {
            get {
                return ResourceManager.GetString("ItemDescription2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Madde No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemNo {
            get {
                return ResourceManager.GetString("ItemNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Ürünün Durumu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ItemStatus {
            get {
                return ResourceManager.GetString("ItemStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kreon Onayladı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string KreonApproved {
            get {
                return ResourceManager.GetString("KreonApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Son Değişiklik Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LastModifiedDate {
            get {
                return ResourceManager.GetString("LastModifiedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Son Degisiklik Kullanıcı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LastModifiedUser {
            get {
                return ResourceManager.GetString("LastModifiedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Lojistik benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Logistic {
            get {
                return ResourceManager.GetString("Logistic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Lojistik Firmasından Çıktı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string LogisticsCompanyDelivered {
            get {
                return ResourceManager.GetString("LogisticsCompanyDelivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Üretici Firma benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Uretici Beyanı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ManufacturerDeclaration {
            get {
                return ResourceManager.GetString("ManufacturerDeclaration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Üretici Beyanı Gerekli mi? benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ManufacturerDeclarationRequired {
            get {
                return ResourceManager.GetString("ManufacturerDeclarationRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eksik CE benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string MissingCE {
            get {
                return ResourceManager.GetString("MissingCE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eksik Dökümanlar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string MissingDoc {
            get {
                return ResourceManager.GetString("MissingDoc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eksik Resimliler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string MissingPictures {
            get {
                return ResourceManager.GetString("MissingPictures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Mal Tanımı benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NewItemDefinition {
            get {
                return ResourceManager.GetString("NewItemDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Ürünler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NewItems {
            get {
                return ResourceManager.GetString("NewItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Şifre benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NewPassword {
            get {
                return ResourceManager.GetString("NewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Yeni Şifre (Tekrar) benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NewPassword2 {
            get {
                return ResourceManager.GetString("NewPassword2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Numaralı Sipariş benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string NumberedOrder {
            get {
                return ResourceManager.GetString("NumberedOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eski Stok No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OldItemNo {
            get {
                return ResourceManager.GetString("OldItemNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eski Ürünler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OldItems {
            get {
                return ResourceManager.GetString("OldItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Eski Şifre benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OldPassword {
            get {
                return ResourceManager.GetString("OldPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sipariş Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OrderDate {
            get {
                return ResourceManager.GetString("OrderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Siparişler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Menşei benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Origin {
            get {
                return ResourceManager.GetString("Origin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Menşei Şehadetnamesi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OriginCertificate {
            get {
                return ResourceManager.GetString("OriginCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Menşei Şehadetnamesi Gerekli mi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string OriginCertificateNecessary {
            get {
                return ResourceManager.GetString("OriginCertificateNecessary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Varsa Diğer 1 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Other1 {
            get {
                return ResourceManager.GetString("Other1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Varsa Diğer 2 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Other2 {
            get {
                return ResourceManager.GetString("Other2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Adet benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Pcs {
            get {
                return ResourceManager.GetString("Pcs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Bekleyen Siparişler benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PendingPromisedDate {
            get {
                return ResourceManager.GetString("PendingPromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Resim benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Picture {
            get {
                return ResourceManager.GetString("Picture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Portal Durumu benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PortalStatus {
            get {
                return ResourceManager.GetString("PortalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bir Önceki Termin Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PreviousPromisedDate {
            get {
                return ResourceManager.GetString("PreviousPromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PromisedDate {
            get {
                return ResourceManager.GetString("PromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Verildi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PromisedDateEntered {
            get {
                return ResourceManager.GetString("PromisedDateEntered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Verilemedi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PromisedDateNotSaved {
            get {
                return ResourceManager.GetString("PromisedDateNotSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Termin Veilmesi Bekleniyor benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PromisedDatePending {
            get {
                return ResourceManager.GetString("PromisedDatePending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Taahhüt Edilen Teslim Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PromisedReceiptDate {
            get {
                return ResourceManager.GetString("PromisedReceiptDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satınalma Açıklaması benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string PurchaseDescription {
            get {
                return ResourceManager.GetString("PurchaseDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Miktar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Alış Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ReceiveDate {
            get {
                return ResourceManager.GetString("ReceiveDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kırmızı Satırlar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RedLines {
            get {
                return ResourceManager.GetString("RedLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Kalan Miktar benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RemainderQuantity {
            get {
                return ResourceManager.GetString("RemainderQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   İstenilen Gönderim Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string ReqDeliveryDate {
            get {
                return ResourceManager.GetString("ReqDeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   İstenilen Tarih benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequestedDate {
            get {
                return ResourceManager.GetString("RequestedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal Tanımı İçin Gerekli benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string RequiredForItemDefinition {
            get {
                return ResourceManager.GetString("RequiredForItemDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Revizyon benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Revision {
            get {
                return ResourceManager.GetString("Revision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Seçiniz benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Mal Tanımı Seç benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string SelectItemDefinition {
            get {
                return ResourceManager.GetString("SelectItemDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Gönder benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Bilgi İstenmemiş benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Unrequsted {
            get {
                return ResourceManager.GetString("Unrequsted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Profil benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string UserProfile {
            get {
                return ResourceManager.GetString("UserProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi Özel Durum Açıklama benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorDesc1 {
            get {
                return ResourceManager.GetString("VendorDesc1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi Özel Durum Açıklama 2 benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorDesc2 {
            get {
                return ResourceManager.GetString("VendorDesc2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satıcıdan çıkış tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorExitDate {
            get {
                return ResourceManager.GetString("VendorExitDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Satıcı No benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorNo {
            get {
                return ResourceManager.GetString("VendorNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Tedarikçi Standart Teslim Tarihi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string VendorStandDelDate {
            get {
                return ResourceManager.GetString("VendorStandDelDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Giriş Ambarı İşlem Süresi benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string WarehouseProcessingTime {
            get {
                return ResourceManager.GetString("WarehouseProcessingTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Haftalık benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string Weekly {
            get {
                return ResourceManager.GetString("Weekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   İş Günü benzeri yerelleştirilmiş bir dize arar.
        /// </summary>
        internal static string WorkDay {
            get {
                return ResourceManager.GetString("WorkDay", resourceCulture);
            }
        }
    }
}
