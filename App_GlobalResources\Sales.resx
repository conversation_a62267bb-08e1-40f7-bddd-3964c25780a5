﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddItem" xml:space="preserve">
    <value>Ürün Ekle</value>
  </data>
  <data name="AddNewFile" xml:space="preserve">
    <value>Yeni Dosya Ekle</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Oluştur</value>
  </data>
  <data name="CreateBid" xml:space="preserve">
    <value>Teklif Oluştur</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Tarih</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Sil</value>
  </data>
  <data name="DeleteFile" xml:space="preserve">
    <value>Dosyayı Sil</value>
  </data>
  <data name="DeleteBid" xml:space="preserve">
    <value>Teklifi Sil</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="DownloadFile" xml:space="preserve">
    <value>Dosyayı İndir</value>
  </data>
  <data name="Drafts" xml:space="preserve">
    <value>Taslaklar</value>
  </data>
  <data name="GoToDrafts" xml:space="preserve">
    <value>Taslaklara  Git</value>
  </data>
  <data name="iDeleteBid" xml:space="preserve">
    <value>Numaralı Teklifi silmek istediğinizden eminmisiniz ?</value>
  </data>
  <data name="iDraftsInfo" xml:space="preserve">
    <value>Taslaklarda Bekleyen öğerniz var</value>
  </data>
  <data name="iNotSaved" xml:space="preserve">
    <value>Kaydedilemedi! Lütfen Sonra Tekrar Deneyin</value>
  </data>
  <data name="iSavedSuccessfully" xml:space="preserve">
    <value>Kaydedildi</value>
  </data>
  <data name="ItemDetailQuestions" xml:space="preserve">
    <value>Ürün Detay Soruları</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Ürünler</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>no</value>
  </data>
  <data name="Note" xml:space="preserve">
    <value>Not</value>
  </data>
  <data name="BidInformations" xml:space="preserve">
    <value>Teklif Bilgileri</value>
  </data>
  <data name="BidNo" xml:space="preserve">
    <value>Teklif No</value>
  </data>
  <data name="Bids" xml:space="preserve">
    <value>Teklifler</value>
  </data>
  <data name="ProjectName" xml:space="preserve">
    <value>Proje Adı</value>
  </data>
  <data name="ProjectNo" xml:space="preserve">
    <value>Proje No</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Adet</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Kaydet</value>
  </data>
  <data name="SelectCustomer" xml:space="preserve">
    <value>Müşteri Seçimi</value>
  </data>
  <data name="SendBid" xml:space="preserve">
    <value>Teklifi Gönder</value>
  </data>
  <data name="StockCode" xml:space="preserve">
    <value>Stok Kodu</value>
  </data>
  <data name="UpdateBidInformation" xml:space="preserve">
    <value>Teklif Bilgilerini Güncelle</value>
  </data>
  <data name="UploadedFiles" xml:space="preserve">
    <value>Yüklenen Dosyalar</value>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>Dosyayı Yükle</value>
  </data>
  <data name="AcceptBid" xml:space="preserve">
    <value>Teklifi onayla</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Ekle</value>
  </data>
  <data name="AddItemUseForm" xml:space="preserve">
    <value>Form kullanarak ürün ekle</value>
  </data>
  <data name="BidDate" xml:space="preserve">
    <value>Teklif Tarihi</value>
  </data>
  <data name="BidDetail" xml:space="preserve">
    <value>Teklif Detayı</value>
  </data>
  <data name="BidStatus" xml:space="preserve">
    <value>Teklif Durumu</value>
  </data>
  <data name="BidsUser" xml:space="preserve">
    <value>Teklifi Oluşturan Kullanıcı</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Müşteri Adı</value>
  </data>
  <data name="DownloadAllFiles" xml:space="preserve">
    <value>Tüm Dosyaları İndir</value>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>Taslak</value>
  </data>
  <data name="Files" xml:space="preserve">
    <value>Dosyalar</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Bilgi</value>
  </data>
  <data name="iNoBidsInDrafts" xml:space="preserve">
    <value>Taslakta teklifiniz bulunmamaktadır.</value>
  </data>
  <data name="ItemAddedList" xml:space="preserve">
    <value>Ürün Listeye Eklendi</value>
  </data>
  <data name="ItemDescription" xml:space="preserve">
    <value>Ürün Açıklaması</value>
  </data>
  <data name="ItemDetails" xml:space="preserve">
    <value>Ürün Detayı</value>
  </data>
  <data name="ItemInformations" xml:space="preserve">
    <value>Ürün Bilgileri</value>
  </data>
  <data name="ItemList" xml:space="preserve">
    <value>Ürün Listesi</value>
  </data>
  <data name="ItemNote" xml:space="preserve">
    <value>Ürün Notu</value>
  </data>
  <data name="ItemQuantityInformation" xml:space="preserve">
    <value>Ürün Adet Bilgisi</value>
  </data>
  <data name="LastModify" xml:space="preserve">
    <value>Son Değiştirme</value>
  </data>
  <data name="LineNo" xml:space="preserve">
    <value>Sıra No</value>
  </data>
  <data name="Log" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="PrintItemDetails" xml:space="preserve">
    <value>Ürün Detay Çıktısı</value>
  </data>
  <data name="PrintQuestions" xml:space="preserve">
    <value>Yazdır</value>
  </data>
  <data name="SalesUnit" xml:space="preserve">
    <value>Satış Birimi</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Seçiniz</value>
  </data>
  <data name="SelectItemInItemList" xml:space="preserve">
    <value>Ürün listesinde olan bir ürünü ekle</value>
  </data>
  <data name="SelectItemPreviousBids" xml:space="preserve">
    <value>Daha önceki tekliflerden ürün ekle</value>
  </data>
  <data name="StockName" xml:space="preserve">
    <value>Stok Adı</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Toplam</value>
  </data>
  <data name="Tutation" xml:space="preserve">
    <value>Tutar</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Birim Fiyat</value>
  </data>
</root>