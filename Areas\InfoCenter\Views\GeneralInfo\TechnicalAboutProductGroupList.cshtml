@model Kreon.Areas.InfoCenter.Models.TeknikSorularModel

@{
    ViewBag.Title = "TechinicalAboutProductGroupList";
    Layout = "~/Views/Shared/Layout.cshtml";
}
@section styles{
    <style>
        .soltaraf {
            max-height: 500px !important;
            overflow: scroll !important;
        }
    </style>
}

@Html.Partial("~/Areas/InfoCenter/Views/Shared/_BaseSoruOrtakButon.cshtml", Model.BaseSoruOrtakButonModel)

<div class="row krn-toolbar">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <ol class="breadcrumb">
            <li>
                <a href="javascript:" onclick="LinkeGit(0)">
                    <span class="glyphicon glyphicon-folder-open"></span>
                    @Resources.Panel.Home
                </a>
            </li>
            @foreach (var item in Model.BreadCumbList)
            {
                <li>
                    <a href="javascript:" onclick="LinkeGit(@item.ItemID)">
                        <span class="glyphicon glyphicon-folder-open"></span>  @item.ItemName
                    </a>
                </li>
            }


        </ol>
    </div>

    <div class="col-md-3 soltaraf">
        @foreach (var item in Model.SolTaraf)
        {
            string durum = "";

            if (item.ItemId == Model.ParentItem.ItemID)
            {
                durum = "active kreonbg";
            }
            <ul class="list-group">
                <li class="list-group-item @durum" onclick="LinkeGit(@item.ItemId)">
                    @item.ItemName
                </li>

            </ul>
        }


    </div>
    <div class="col-md-9 col-sm-9 col-xs-12">

        @if (Model.GenelState > 0)
        {
            <div class="">
                @{
                    int i = 0;

                    foreach (var item in Model.TreeModels)
                    {
                        i++;
                        <div class="col-md-4 col-sm-4 col-xs-6" style="padding-bottom: 15px;">
                            <div class="row">
                                <div class="col-md-12" style="padding-bottom: 3px;">
                                    <a href="javascript:" class="btn btn-block kreonbg" onclick="LinkeGit(@item.ItemId)"><i class="glyphicon glyphicon-folder-close"></i> @item.ItemName</a>
                                </div>
                            </div>
                        </div>


                    }

                }
            </div>
        }


    </div>


</div>

@section scripts
{
    <script>
        function LinkeGit(id) {
            var url = "/InfoCenter/GeneralInfo/TechnicalAboutProductGroupList/" + id;
            window.location.href = url;
        }
    </script>
}